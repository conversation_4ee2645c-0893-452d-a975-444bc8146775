[2025/08/07 15:31:42.763 +08:00] [INFO] [logutil/logutil.go:165] ["Log directory"] [configDir="D:\\AAAAA-wq-work\\access\\milvus_data\\logs"]
[2025/08/07 15:31:42.763 +08:00] [INFO] [logutil/logutil.go:166] ["Set log file to "] [path="D:\\AAAAA-wq-work\\access\\milvus_data\\logs/standalone-9.log"]
[2025/08/07 15:31:42.763 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="indexnode-10\n"] [filePath="\\tmp\\milvus\\server_id_28564"]
[2025/08/07 15:31:42.764 +08:00] [DEBUG] [indexnode/indexnode.go:204] ["IndexNode init session successful"] [serverID=10]
[2025/08/07 15:31:42.764 +08:00] [DEBUG] [indexnode/indexnode.go:212] ["IndexNode NewMinIOKV succeeded"]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/index_coord.go:207] ["IndexCoord init"] [stateCode=Initializing]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/index_coord.go:214] ["IndexCoord try to connect etcd"]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/meta_table.go:98] ["IndexCoord metaTable reloadFromKV load indexes"]
[2025/08/07 15:31:42.764 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="querynode-9\n"] [filePath="\\tmp\\milvus\\server_id_28564"]
[2025/08/07 15:31:42.764 +08:00] [INFO] [querynode/query_node.go:211] ["QueryNode init session"] [nodeID=9] ["node address"=**************:40002]
[2025/08/07 15:31:42.764 +08:00] [INFO] [querynode/query_node.go:327] ["QueryNode init rateCollector done"] [nodeID=9]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/meta_table.go:118] ["IndexCoord metaTable reloadFromKV success"]
[2025/08/07 15:31:42.764 +08:00] [INFO] [querynode/query_node.go:337] ["queryNode try to connect etcd success"] [MetaRootPath=by-dev/meta]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/index_coord.go:221] ["IndexCoord try to connect etcd success"]
[2025/08/07 15:31:42.764 +08:00] [INFO] [querynode/segment_loader.go:978] ["SegmentLoader created"] [ioPoolSize=96] [cpuPoolSize=12]
[2025/08/07 15:31:42.764 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=indexnode] [key=indexnode-1] [address=**************:40004]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/index_coord.go:225] [IndexCoord] ["session number"=1] [revision=35]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/index_coord.go:230] ["IndexCoord get node sessions from etcd"] ["bind mode"=false] ["node address"=localhost:22930]
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexcoord/node_manager.go:95] ["IndexCoord addNode"] [nodeID=1] ["node address"=**************:40004]
[2025/08/07 15:31:42.764 +08:00] [DEBUG] [indexnode/indexnode.go:218] ["Init IndexNode finished"] []
[2025/08/07 15:31:42.764 +08:00] [INFO] [indexnode/service.go:78] ["IndexNode init done ..."]
[2025/08/07 15:31:42.764 +08:00] [DEBUG] [indexnode/indexnode.go:233] [IndexNode] [State=Healthy]
[2025/08/07 15:31:42.764 +08:00] [DEBUG] [indexnode/indexnode.go:236] ["IndexNode start finished"] []
[2025/08/07 15:31:42.765 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=indexnode] [ServerID=10]
[2025/08/07 15:31:42.765 +08:00] [INFO] [indexnode/task_scheduler.go:240] ["IndexNode TaskScheduler start build loop ..."]
[2025/08/07 15:31:42.765 +08:00] [INFO] [gc/gc_tuner.go:130] ["GC Helper initialized."] ["Initial GoGC"=100] [minimumGOGC=30] [maximumGOGC=200] [memoryThreshold=38498125824]
[2025/08/07 15:31:42.765 +08:00] [INFO] [querynode/query_node.go:382] ["query node init successfully"] [queryNodeID=9] [IP=**************] [Port=40002]
[2025/08/07 15:31:42.765 +08:00] [INFO] [querynode/service.go:235] ["QueryNode init done ..."]
[2025/08/07 15:31:42.765 +08:00] [INFO] [querynode/query_node.go:411] ["query node start successfully"] [queryNodeID=9] [IP=**************] [Port=40002]
[2025/08/07 15:31:42.765 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=querynode] [ServerID=9]
[2025/08/07 15:31:42.766 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:42.766 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/indexnode-10] [value="{\"ServerID\":10,\"ServerName\":\"indexnode\",\"Address\":\"**************:40004\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:31:42.766 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=indexnode] [serverID=10]
[2025/08/07 15:31:42.766 +08:00] [INFO] [indexnode/indexnode.go:133] ["IndexNode Register Finished"]
[2025/08/07 15:31:42.767 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:31:42.767 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/querynode-9] [value="{\"ServerID\":9,\"ServerName\":\"querynode\",\"Address\":\"**************:40002\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:31:42.767 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=querynode] [serverID=9]
[2025/08/07 15:31:42.767 +08:00] [INFO] [querynode/query_node.go:219] ["QueryNode Register Finished"]
[2025/08/07 15:31:42.767 +08:00] [INFO] [indexnode/service.go:214] ["IndexNode Register etcd success"]
[2025/08/07 15:31:42.767 +08:00] [INFO] [indexnode/service.go:82] ["IndexNode start done ..."]
[2025/08/07 15:31:42.767 +08:00] [DEBUG] [components/index_node.go:55] ["IndexNode successfully started"]
[2025/08/07 15:31:42.767 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:31:42.767 +08:00] [INFO] [querynode/service.go:240] ["QueryNode start done ..."]
[2025/08/07 15:31:42.767 +08:00] [DEBUG] [components/query_node.go:57] ["QueryNode successfully started"]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=indexcoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=indexcoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=indexcoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=indexcoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=indexcoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=indexcoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=indexcoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:31:42.768 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=indexcoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:31:42.768 +08:00] [INFO] [rootcoord/service.go:221] ["RootCoord start to create QueryCoord client"]
[2025/08/07 15:31:42.768 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:42.768 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.768 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=indexnode] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=indexnode] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=indexnode] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=indexnode] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=indexnode] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=indexnode] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=indexnode] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=indexnode] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:31:42.770 +08:00] [DEBUG] [indexcoord/node_manager.go:59] ["IndexCoord NodeManager setClient"] [nodeID=1]
[2025/08/07 15:31:42.770 +08:00] [INFO] [indexcoord/node_manager.go:69] ["IndexNode NodeManager setClient success"] [nodeID=1] ["IndexNode num"=1]
[2025/08/07 15:31:42.770 +08:00] [INFO] [indexcoord/index_coord.go:253] [IndexCoord] ["IndexNode number"=1]
[2025/08/07 15:31:42.770 +08:00] [INFO] [indexcoord/index_coord.go:264] ["IndexCoord new minio chunkManager success"]
[2025/08/07 15:31:42.770 +08:00] [INFO] [indexcoord/flush_segment_watcher.go:87] ["flushSegmentWatcher reloadFromKV"]
[2025/08/07 15:31:42.770 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=querynode] [key=by-dev/meta/session/querynode-9]
[2025/08/07 15:31:42.770 +08:00] [INFO] [sessionutil/session_util.go:690] ["watch services"] ["add kv"="key:\"by-dev/meta/session/indexnode-10\" create_revision:36 mod_revision:36 version:1 value:\"{\\\"ServerID\\\":10,\\\"ServerName\\\":\\\"indexnode\\\",\\\"Address\\\":\\\"**************:40004\\\",\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637499159321 "]
[2025/08/07 15:31:42.770 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionAddEvent]
[2025/08/07 15:31:42.772 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:42.772 +08:00] [INFO] [indexcoord/flush_segment_watcher.go:109] ["flushSegmentWatcher reloadFromKV success"] [etcdRevision=37]
[2025/08/07 15:31:42.772 +08:00] [INFO] [indexcoord/index_coord.go:278] ["IndexCoord new task scheduler success"]
[2025/08/07 15:31:42.772 +08:00] [INFO] [indexcoord/index_coord.go:281] ["IndexCoord init finished"]
[2025/08/07 15:31:42.772 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:42.772 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.772 +08:00] [INFO] [indexcoord/service.go:151] ["IndexCoord try to wait for RootCoord ready"]
[2025/08/07 15:31:42.772 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.772 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.773 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=0): server ID mismatch"]
[2025/08/07 15:31:42.773 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.773 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=querycoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=querycoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=querycoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=querycoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=querycoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=querycoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=querycoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:31:42.774 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=querycoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:31:42.774 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:42.774 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.775 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=12]
[2025/08/07 15:31:42.775 +08:00] [INFO] [rootcoord/root_coord.go:173] ["update rootcoord state"] [state=Initializing]
[2025/08/07 15:31:42.776 +08:00] [INFO] [tso/tso.go:121] ["sync and save timestamp"] [last=2025/08/07 15:29:18.950 +08:00] [save=2025/08/07 15:31:45.775 +08:00] [next=2025/08/07 15:31:42.775 +08:00]
[2025/08/07 15:31:42.776 +08:00] [INFO] [rootcoord/root_coord.go:385] ["id allocator initialized"] [root_path=by-dev/kv] [sub_path=gid] [key=idTimestamp]
[2025/08/07 15:31:42.776 +08:00] [INFO] [tso/tso.go:121] ["sync and save timestamp"] [last=2025/08/07 15:29:21.977 +08:00] [save=2025/08/07 15:31:45.776 +08:00] [next=2025/08/07 15:31:42.776 +08:00]
[2025/08/07 15:31:42.776 +08:00] [INFO] [rootcoord/root_coord.go:401] ["tso allocator initialized"] [root_path=by-dev/kv] [sub_path=gid] [key=idTimestamp]
[2025/08/07 15:31:42.776 +08:00] [DEBUG] [rootcoord/suffix_snapshot.go:558] ["suffix snapshot GC goroutine start!"]
[2025/08/07 15:31:42.776 +08:00] [INFO] [rootcoord/meta_table.go:148] ["recover databases"] ["num of dbs"=1]
[2025/08/07 15:31:42.776 +08:00] [INFO] [rootcoord/meta_table.go:186] ["recover collections from db"] [collection_num=0] [partition_num=0]
[2025/08/07 15:31:42.776 +08:00] [INFO] [rootcoord/meta_table.go:203] ["meta table recovery finished"]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_0]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_1]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_2]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_3]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_4]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_5]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_6]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_7]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_8]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_9]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_10]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_11]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_12]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_13]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_14]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [WARN] [server/rocksmq_impl.go:358] ["rocksmq topic already exists "] [topic=by-dev-rootcoord-dml_15]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:31:42.777 +08:00] [INFO] [rootcoord/dml_channels.go:178] ["init dml channels"] [prefix=by-dev-rootcoord-dml] [num=16]
[2025/08/07 15:31:42.777 +08:00] [INFO] [rootcoord/timeticksync.go:226] ["Add session for timeticksync"] [serverID=12]
[2025/08/07 15:31:42.777 +08:00] [INFO] [rootcoord/root_coord.go:470] ["RootCoord init QuotaCenter done"]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rootcoord/kv_catalog.go:727] ["the key has existed"] [key=root-coord/credential/roles/admin]
[2025/08/07 15:31:42.777 +08:00] [DEBUG] [rootcoord/kv_catalog.go:727] ["the key has existed"] [key=root-coord/credential/roles/public]
[2025/08/07 15:31:42.777 +08:00] [INFO] [rootcoord/service.go:162] ["RootCoord init done ..."]
[2025/08/07 15:31:42.777 +08:00] [INFO] [rootcoord/service.go:299] ["RootCoord Core start ..."]
[2025/08/07 15:31:42.777 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=rootcoord] [ServerID=12]
[2025/08/07 15:31:42.779 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.779 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:42.779 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:42.779 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.779 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.779 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.781 +08:00] [INFO] [gc/gc_tuner.go:84] ["GC Tune done"] ["previous GOGC"=100] ["heapuse "=31] ["total memory"=18236] ["next GC"=40] ["new GOGC"=200] [gc-pause=0s] [gc-pause-end=1754551902781591500]
[2025/08/07 15:31:42.782 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:42.782 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:42.782 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:42.782 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:42.782 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:42.782 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:42.782 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:42.783 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:42.783 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:42.891 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.891 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:42.891 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:42.891 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.891 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.891 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.954 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:42.954 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.954 +08:00] [INFO] [datacoord/service.go:123] ["create IndexCoord client for DataCoord done"]
[2025/08/07 15:31:42.954 +08:00] [INFO] [datacoord/service.go:126] ["init IndexCoord client for DataCoord"]
[2025/08/07 15:31:42.954 +08:00] [INFO] [datacoord/service.go:131] ["init IndexCoord client for DataCoord done"]
[2025/08/07 15:31:42.954 +08:00] [INFO] [datacoord/service.go:159] ["network port"] [port=40005]
[2025/08/07 15:31:43.003 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.003 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.003 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.003 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.003 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.003 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.065 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:43.066 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=13]
[2025/08/07 15:31:43.067 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [sessionutil/session_util.go:561] ["SessionUtil GetSessions "] [prefix=datanode] [key=datanode-6] [address=**************:40006]
[2025/08/07 15:31:43.067 +08:00] [INFO] [datacoord/server.go:439] ["DataCoord success to get DataNode sessions"] [sessions="{\"datanode-6\":{\"ServerID\":6,\"ServerName\":\"datanode\",\"Address\":\"**************:40006\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}}"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [datacoord/channel_manager.go:341] ["register node with no reassignment"] ["registered node"=6]
[2025/08/07 15:31:43.067 +08:00] [INFO] [datacoord/channel_manager.go:171] ["starting etcd states checker"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [datacoord/channel_manager.go:176] ["starting background balance checker"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [datacoord/channel_manager.go:179] ["cluster start up"] [nodes="[6]"] [oNodes="[]"] ["old onlines"="[]"] ["new onlines"="[6]"] [offLines="[]"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=querycoord] [key=querycoord] [address=**************:40001]
[2025/08/07 15:31:43.067 +08:00] [INFO] [datacoord/segment_reference_manager.go:41] ["create a new segment reference manager"]
[2025/08/07 15:31:43.067 +08:00] [INFO] [datacoord/segment_reference_manager.go:184] ["recovery reference lock on segments by online nodes"] ["online nodeIDs"="[7]"]
[2025/08/07 15:31:43.068 +08:00] [INFO] [datacoord/segment_reference_manager.go:204] ["recovery reference lock on segments by online nodes successfully"] ["online nodeIDs"="[7]"] ["offline nodeIDs"={}]
[2025/08/07 15:31:43.068 +08:00] [INFO] [datacoord/segment_reference_manager.go:75] ["create new segment reference manager successfully"]
[2025/08/07 15:31:43.068 +08:00] [INFO] [datacoord/garbage_collector.go:71] ["GC with option"] [enabled=true] [interval=1h0m0s] [missingTolerance=1h0m0s] [dropTolerance=3h0m0s]
[2025/08/07 15:31:43.068 +08:00] [INFO] [datacoord/service.go:264] ["DataCoord init done ..."]
[2025/08/07 15:31:43.068 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=datacoord] [ServerID=13]
[2025/08/07 15:31:43.112 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.112 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.112 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.112 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.112 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.112 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.237 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.237 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.237 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.237 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.237 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.237 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.348 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.348 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.348 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.348 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.348 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.348 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.459 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.460 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.460 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.460 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.460 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.460 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.570 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.570 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.570 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.570 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.570 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.570 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.679 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.679 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.679 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.679 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.679 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.679 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.773 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:43.773 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:43.773 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:43.773 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:43.773 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:43.773 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:43.773 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:43.773 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:43.773 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:43.788 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.788 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.788 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.788 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.788 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.788 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:43.898 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:43.898 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:43.898 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:43.898 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:43.898 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:43.898 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.009 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.009 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.009 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.009 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.009 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.009 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.118 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.118 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.118 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.118 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.118 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.118 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.228 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.228 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.228 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.228 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.228 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.228 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.338 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.338 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.338 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.338 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.338 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.338 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.448 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.448 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.448 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.448 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.448 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.448 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.556 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.556 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.556 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.556 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.556 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.556 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.666 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.666 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.666 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.666 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.666 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.666 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.775 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.775 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.775 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.775 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.775 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.775 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.885 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.885 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.885 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.885 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.885 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.885 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:44.994 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:44.994 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:44.994 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:44.994 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:44.994 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:44.994 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.103 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.103 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.103 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.103 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.103 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.103 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.212 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.212 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.212 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.212 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.212 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.212 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.321 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.321 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.321 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.321 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.321 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.321 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.431 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.431 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.431 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.431 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.431 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.431 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.542 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.542 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.542 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.542 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.542 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.542 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.651 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.651 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.651 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.651 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.651 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.651 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.761 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.761 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.761 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.761 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.761 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.761 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.776 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:45.776 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:45.776 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:45.776 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:45.776 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:45.776 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:45.776 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:45.776 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:45.776 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:45.810 +08:00] [WARN] [retry/retry.go:41] ["retry func failed"] ["retry time"=4] [error="function CompareAndSwap error for compare is false for key: rootcoord"]
[2025/08/07 15:31:45.871 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.871 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.871 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.871 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.872 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.872 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:45.981 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:45.981 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:45.981 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:45.981 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:45.981 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:45.981 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.090 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.090 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.090 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.090 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.090 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.090 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.122 +08:00] [WARN] [retry/retry.go:41] ["retry func failed"] ["retry time"=4] [error="function CompareAndSwap error for compare is false for key: datacoord"]
[2025/08/07 15:31:46.199 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.199 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.199 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.199 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.199 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.199 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.309 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.309 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.309 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.309 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.309 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.309 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.419 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.419 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.419 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.419 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.419 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.419 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.530 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.530 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.530 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.530 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.530 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.530 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.641 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.641 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.641 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.641 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.641 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.642 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.750 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.750 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.750 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.750 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.750 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.750 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.861 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.861 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.861 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.861 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.861 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.861 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:46.971 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:46.971 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:46.971 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:46.971 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:46.971 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:46.971 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.080 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.080 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.080 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.080 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.080 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.080 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.192 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.192 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.192 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.192 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.192 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.192 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.302 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.302 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.302 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.302 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.302 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.302 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.413 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.413 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.413 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.413 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.413 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.413 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.524 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.524 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.524 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.524 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.524 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.524 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.634 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.634 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.634 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.634 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.634 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.634 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.745 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.745 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.745 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.745 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.745 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.745 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.853 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.853 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.853 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.853 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.853 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.853 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:47.964 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:47.964 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:47.964 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:47.964 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:47.964 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:47.964 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.074 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.074 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.075 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.075 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.075 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.075 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.185 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.185 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.185 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.185 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.185 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.185 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.295 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.295 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.295 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.295 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.295 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.295 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.403 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.403 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.403 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.403 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.403 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.403 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.514 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.514 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.514 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.514 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.514 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.514 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.623 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.623 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.623 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.623 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.623 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.623 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.731 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.731 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.731 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.731 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.731 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.731 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.840 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.840 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.840 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.840 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.840 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.840 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:48.950 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:48.950 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:48.950 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:48.950 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:48.950 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:48.950 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.061 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.061 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.061 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.061 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.061 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.061 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.173 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.173 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.173 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.173 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.173 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.173 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.295 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.295 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.295 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.295 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.295 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.295 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.404 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.404 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.404 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.404 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.404 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.404 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.511 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.511 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.511 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.511 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.511 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.511 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.619 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.619 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.619 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.619 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.619 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.619 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.728 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.728 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.728 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.728 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.728 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.728 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.789 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:49.789 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:49.789 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:49.789 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:49.789 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:49.789 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:49.789 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:49.789 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:49.789 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:49.837 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.838 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.838 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.838 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.838 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.838 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:49.948 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:49.948 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:49.948 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:49.948 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:49.948 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:49.948 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.056 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.056 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.056 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.056 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.056 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.056 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.165 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.165 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.165 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.165 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.165 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.165 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.276 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.276 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.276 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.276 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.276 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.276 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.385 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.385 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.385 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.385 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.385 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.385 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.494 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.494 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.494 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.494 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.494 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.494 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.604 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.604 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.604 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.604 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.604 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.604 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.713 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.713 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.713 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.713 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.713 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.713 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.837 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.837 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.837 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.837 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.837 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.837 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:50.950 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:50.950 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:50.950 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:50.950 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:50.950 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:50.950 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.059 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.059 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.059 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.059 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.059 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.059 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.168 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.168 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.168 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.168 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.168 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.168 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.276 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.277 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.277 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.277 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.277 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.277 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.387 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.387 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.387 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.387 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.387 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.387 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.497 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.497 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.497 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.497 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.497 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.497 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.606 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.606 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.606 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.606 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.606 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.606 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.717 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.717 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.717 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.717 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.717 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.717 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.827 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.827 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.827 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.827 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.827 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.827 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:51.939 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:51.939 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:51.939 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:51.939 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:51.939 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:51.939 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.049 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.049 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.049 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.049 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.049 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.049 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.158 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.158 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.158 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.158 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.158 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.158 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.283 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.283 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.283 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.283 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.283 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.283 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.395 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.395 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.395 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.395 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.395 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.395 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.506 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.506 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.506 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.506 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.506 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.506 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.616 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.616 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.616 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.616 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.616 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.616 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.727 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.727 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.727 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.727 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.727 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.727 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.836 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.836 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.836 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.836 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.836 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.836 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:52.961 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:52.961 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:52.961 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:52.961 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:52.961 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:52.961 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.073 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.073 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.073 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.073 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.073 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.073 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.182 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.182 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.182 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.182 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.182 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.182 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.291 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.291 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.291 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.291 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.291 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.291 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.400 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.400 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.400 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.400 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.400 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.400 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.511 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.511 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.511 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.511 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.511 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.511 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.619 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.619 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.619 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.619 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.619 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.619 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.729 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.729 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.729 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.729 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.729 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.729 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.838 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.838 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.838 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.838 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.838 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.838 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:53.947 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:53.947 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:53.947 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:53.947 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:53.947 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:53.947 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.057 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.057 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.057 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.057 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.057 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.057 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.167 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.167 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.167 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.167 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.167 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.167 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.275 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.275 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.275 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.275 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.275 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.275 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.386 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.386 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.386 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.386 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.386 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.386 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.494 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.494 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.494 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.494 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.494 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.494 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.604 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.604 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.604 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.604 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.604 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.604 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.714 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.714 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.714 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.714 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.714 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.714 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.822 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.822 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.822 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.822 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.822 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.822 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:54.933 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:54.933 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:54.933 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:54.933 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:54.933 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:54.933 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.041 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.041 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.041 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.041 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.041 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.041 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.150 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.150 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.150 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.150 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.150 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.150 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.259 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.259 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.259 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.259 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.259 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.259 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.368 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.368 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.368 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.368 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.368 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.368 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.477 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.477 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.477 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.477 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.477 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.477 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.588 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.589 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.589 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.589 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.589 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.589 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.696 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.696 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.696 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.696 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.696 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.696 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.804 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.804 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.804 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.804 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.804 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.804 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:55.914 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:55.914 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:55.914 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:55.914 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:55.914 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:55.914 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.025 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.025 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.025 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.025 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.025 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.025 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.136 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.136 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.136 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.136 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.136 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.136 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.246 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.246 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.246 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.246 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.246 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.246 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.356 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.356 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.356 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.356 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.356 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.356 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.464 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.464 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.464 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.464 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.464 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.464 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.575 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.575 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.575 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.575 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.575 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.575 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.683 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.683 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.683 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.683 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.683 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.683 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.792 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.792 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.792 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.792 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.792 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.792 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:56.903 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:56.903 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:56.903 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:56.903 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:56.903 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:56.903 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.011 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.011 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.011 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.011 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.011 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.011 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.122 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.122 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.122 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.122 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.122 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.122 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.232 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.232 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.232 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.232 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.232 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.232 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.353 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.353 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.353 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.353 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.353 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.353 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.465 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.465 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.465 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.465 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.465 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.465 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.588 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.588 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.588 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.588 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.589 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.589 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.698 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.698 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.698 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.698 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.698 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.698 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.806 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.806 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:57.806 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:57.806 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:57.806 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:57.806 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:57.806 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:57.806 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:57.806 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:57.807 +08:00] [WARN] [retry/retry.go:41] ["retry func failed"] ["retry time"=4] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.807 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.807 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.807 +08:00] [WARN] [retry/retry.go:41] ["retry func failed"] ["retry time"=4] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.807 +08:00] [WARN] [retry/retry.go:41] ["retry func failed"] ["retry time"=4] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.807 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.807 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.807 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.807 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:57.916 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:57.916 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:57.916 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:57.916 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:57.916 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:57.916 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.026 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.026 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.026 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.026 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.026 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.026 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.133 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.133 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.133 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.133 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.133 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.133 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.244 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.244 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.244 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.244 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.244 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.244 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.354 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.354 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.354 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.354 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.354 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.354 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.462 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.462 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.462 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.462 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.462 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.462 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.571 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.571 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.571 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.571 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.571 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.571 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.682 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.682 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.682 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.682 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.682 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.682 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.793 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.793 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.793 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.793 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.793 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.793 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:58.902 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:58.902 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:58.902 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:58.902 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:58.902 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:58.902 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.011 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.011 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.011 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.011 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.011 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.011 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.122 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.122 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.122 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.122 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.122 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.122 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.231 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.231 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.231 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.231 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.231 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.231 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.342 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.342 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.342 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.342 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.342 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.342 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.452 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.452 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.452 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.452 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.452 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.452 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.562 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.562 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.562 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.562 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.562 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.562 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.674 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.674 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.674 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.674 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.674 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.675 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.782 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.782 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.782 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.782 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.782 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.782 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:59.893 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:59.893 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:31:59.893 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:31:59.893 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:59.893 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:59.893 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.002 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.003 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.003 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.003 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.003 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.003 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.112 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.112 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.112 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.112 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.112 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.112 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.239 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.239 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.239 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.239 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.239 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.239 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.365 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.365 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.365 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.365 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.365 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.365 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.475 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.475 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.475 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.475 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.475 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.475 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.586 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.586 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.586 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.586 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.586 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.586 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.697 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.697 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.697 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.697 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.697 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.697 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.807 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.807 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.807 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.807 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.807 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.807 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:00.916 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:00.916 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:00.916 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:00.916 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:00.916 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:00.916 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.026 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.026 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.026 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.026 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.026 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.026 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.134 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.134 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.134 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.134 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.134 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.134 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.244 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.244 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.244 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.244 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.244 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.244 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.353 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.353 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.353 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.353 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.353 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.353 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.464 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.464 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.464 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.464 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.464 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.464 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.575 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.575 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.575 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.575 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.575 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.575 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.683 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.683 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.683 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.683 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.683 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.683 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.794 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.794 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.794 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.794 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.794 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.794 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:01.902 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:01.902 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:01.902 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:01.902 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:01.902 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:01.902 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.010 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.010 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.010 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.010 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.010 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.010 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.120 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.120 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.120 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.120 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.120 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.120 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.231 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.231 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.231 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.231 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.231 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.231 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.342 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.342 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.342 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.342 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.342 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.342 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.451 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.451 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.451 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.451 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.451 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.451 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.562 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.562 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.562 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.562 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.562 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.562 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.672 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.672 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.672 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.672 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.672 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.672 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.782 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.782 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.782 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.782 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.782 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.782 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:02.891 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:02.891 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:02.891 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:02.891 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:02.891 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:02.891 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.017 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.017 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.017 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.017 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.017 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.017 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.129 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.129 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.129 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.129 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.129 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.129 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.254 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.254 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.254 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.254 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.254 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.254 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.365 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.365 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.365 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.365 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.365 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.365 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.477 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.477 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.477 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.477 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.477 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.477 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.589 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.589 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.589 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.589 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.589 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.589 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.697 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.697 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.697 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.697 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.697 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.697 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.806 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.806 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.806 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.807 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.807 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.807 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:03.915 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:03.915 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:03.915 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:03.915 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:03.915 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:03.915 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.011 +08:00] [INFO] [sessionutil/session_util.go:706] ["watch services"] ["delete kv"="key:\"by-dev/meta/session/datanode-6\" create_revision:25 mod_revision:25 version:1 value:\"{\\\"ServerID\\\":6,\\\"ServerName\\\":\\\"datanode\\\",\\\"Address\\\":\\\"**************:40006\\\",\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637461779330 "]
[2025/08/07 15:32:04.011 +08:00] [INFO] [sessionutil/session_util.go:706] ["watch services"] ["delete kv"="key:\"by-dev/meta/session/querycoord\" create_revision:29 mod_revision:29 version:1 value:\"{\\\"ServerID\\\":7,\\\"ServerName\\\":\\\"querycoord\\\",\\\"Address\\\":\\\"**************:40001\\\",\\\"Exclusive\\\":true,\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637461779350 "]
[2025/08/07 15:32:04.011 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionDelEvent]
[2025/08/07 15:32:04.011 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionDelEvent]
[2025/08/07 15:32:04.011 +08:00] [INFO] [sessionutil/session_util.go:706] ["watch services"] ["delete kv"="key:\"by-dev/meta/session/indexnode-1\" create_revision:6 mod_revision:6 version:1 value:\"{\\\"ServerID\\\":1,\\\"ServerName\\\":\\\"indexnode\\\",\\\"Address\\\":\\\"**************:40004\\\",\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637461779233 "]
[2025/08/07 15:32:04.011 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionDelEvent]
[2025/08/07 15:32:04.025 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.025 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.025 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.025 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.025 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.025 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.134 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.134 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.134 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.134 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.134 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.134 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.197 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/datacoord] [value="{\"ServerID\":13,\"ServerName\":\"datacoord\",\"Address\":\"**************:40005\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:32:04.197 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=datacoord] [serverID=13]
[2025/08/07 15:32:04.197 +08:00] [INFO] [datacoord/server.go:242] ["DataCoord Register Finished"]
[2025/08/07 15:32:04.197 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:32:04.197 +08:00] [INFO] [datacoord/server.go:793] ["received datanode unregister"] [address=**************:40006] [serverID=6]
[2025/08/07 15:32:04.197 +08:00] [WARN] [datacoord/channel_manager.go:375] ["deregister node"] ["unregistered node"=6] [updates="[\"{type=Delete,nodeID=6,channels=\\\"[]\\\"}\",\"{type=Add,nodeID=-9223372036854775808,channels=\\\"[]\\\"}\"]"]
[2025/08/07 15:32:04.197 +08:00] [INFO] [datacoord/channel_manager.go:393] ["remove timers for channel of the deregistered node"] [channels="[]"] [nodeID=6]
[2025/08/07 15:32:04.198 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="datacoord-13\n"] [filePath="\\tmp\\milvus\\server_id_28564"]
[2025/08/07 15:32:04.198 +08:00] [INFO] [datacoord/server.go:351] ["DataCoord startup successfully"]
[2025/08/07 15:32:04.198 +08:00] [INFO] [datacoord/service.go:269] ["DataCoord start done ..."]
[2025/08/07 15:32:04.198 +08:00] [DEBUG] [components/data_coord.go:53] ["DataCoord successfully started"]
[2025/08/07 15:32:04.198 +08:00] [WARN] [datacoord/server.go:718] ["there is service offline"] ["server role"=QueryCoord] ["server ID"=7]
[2025/08/07 15:32:04.198 +08:00] [INFO] [datacoord/segment_reference_manager.go:156] ["release reference lock on segments by node"] [nodeID=7]
[2025/08/07 15:32:04.198 +08:00] [INFO] [datacoord/segment_reference_manager.go:179] ["release reference lock on segments by node successfully"] [nodeID=7]
[2025/08/07 15:32:04.244 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.244 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.244 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.244 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.244 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.244 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.274 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=datacoord] [key=by-dev/meta/session/datacoord]
[2025/08/07 15:32:04.353 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.353 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.353 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.353 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.353 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.353 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.463 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.463 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.463 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.463 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.463 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.463 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.573 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.573 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.573 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.573 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.573 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.573 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.683 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.683 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.683 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.683 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.683 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.683 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.793 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.793 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.793 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.793 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.793 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.793 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:04.903 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:04.903 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:04.903 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:04.903 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:04.903 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:04.903 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.011 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.011 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.011 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.011 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.011 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.011 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.119 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.119 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.119 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.119 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.119 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.119 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.226 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.226 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.226 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.226 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.226 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.226 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.335 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.335 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.335 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.335 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.335 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.335 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.445 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.445 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.445 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.445 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.445 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.445 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.556 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.556 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.556 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.556 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.556 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.556 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.665 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.665 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.665 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.665 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.665 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.665 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.775 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.775 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.775 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.775 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.775 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.775 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.884 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.884 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.884 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.884 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.884 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.884 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:05.993 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:05.993 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:05.993 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:05.993 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:05.993 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:05.993 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.102 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.102 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.102 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.102 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.102 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.102 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.213 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.213 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.213 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.213 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.213 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.213 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.323 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.323 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.323 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.323 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.323 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.323 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.447 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.447 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.447 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.447 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.447 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.447 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.557 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.557 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.557 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.557 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.557 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.557 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.663 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.663 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.663 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.663 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.663 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.663 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.775 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.775 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.775 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.775 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.775 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.775 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.899 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:06.900 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:06.900 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:06.900 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:06.900 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:06.900 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:06.902 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/rootcoord] [value="{\"ServerID\":12,\"ServerName\":\"rootcoord\",\"Address\":\"**************:40000\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:32:06.902 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=rootcoord] [serverID=12]
[2025/08/07 15:32:06.902 +08:00] [INFO] [rootcoord/root_coord.go:302] ["RootCoord Register Finished"]
[2025/08/07 15:32:06.902 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:32:06.902 +08:00] [INFO] [rootcoord/proxy_manager.go:85] ["succeed to init sessions on etcd"] [sessions=null] [revision=51]
[2025/08/07 15:32:06.902 +08:00] [INFO] [rootcoord/quota_center.go:132] ["Start QuotaCenter"] [collectInterval/s=3]
[2025/08/07 15:32:06.902 +08:00] [INFO] [rootcoord/proxy_manager.go:104] ["start to watch etcd"]
[2025/08/07 15:32:06.902 +08:00] [INFO] [rootcoord/root_coord.go:173] ["update rootcoord state"] [state=Healthy]
[2025/08/07 15:32:06.902 +08:00] [WARN] [rootcoord/proxy_client_manager.go:220] ["proxy client is empty, RefreshPrivilegeInfoCache will not send to any client"]
[2025/08/07 15:32:06.902 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="rootcoord-12\n"] [filePath="\\tmp\\milvus\\server_id_28564"]
[2025/08/07 15:32:06.902 +08:00] [INFO] [rootcoord/root_coord.go:676] ["rootcoord startup successfully"]
[2025/08/07 15:32:06.902 +08:00] [INFO] [rootcoord/service.go:167] ["RootCoord start done ..."]
[2025/08/07 15:32:06.902 +08:00] [DEBUG] [components/root_coord.go:60] ["RootCoord successfully started"]
[2025/08/07 15:32:07.013 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=rootcoord] [key=by-dev/meta/session/rootcoord]
[2025/08/07 15:32:07.027 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.027 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.027 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.027 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.027 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.027 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.138 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.138 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.138 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.138 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.138 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.138 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.248 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.248 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.248 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.248 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.248 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.248 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.357 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.357 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.357 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.357 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.357 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.357 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.465 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.465 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.465 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.465 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.465 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.465 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.574 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.574 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.574 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.574 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.574 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.574 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.701 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.701 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.701 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.701 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.701 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.701 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.809 +08:00] [WARN] [grpcclient/client.go:370] ["ClientBase Call grpc first call get error"] [role=rootcoord] [error="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:152 github.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).init\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:85 github.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).Run: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"] [errorVerbose="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\n(1) attached stack trace\n  -- stack trace:\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\n  | github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\n  | github.com/milvus-io/milvus/internal/util/retry.Do\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\n  | github.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).init\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:152\n  | github.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:85\n  | github.com/milvus-io/milvus/cmd/components.(*IndexCoord).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/index_coord.go:51\n  | github.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123\n  | runtime.goexit\n  | \tD:/a/_temp/msys64/mingw64/lib/go/src/runtime/asm_amd64.s:1571\nWraps: (2) stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:152 github.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).init\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:85 github.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).Run\nWraps: (3) rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\nError types: (1) *withstack.withStack (2) *errutil.withPrefix (3) *status.Error"]
[2025/08/07 15:32:07.809 +08:00] [WARN] [grpcclient/client.go:370] ["ClientBase Call grpc first call get error"] [role=rootcoord] [error="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:168 github.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).init\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:102 github.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).Run: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"] [errorVerbose="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\n(1) attached stack trace\n  -- stack trace:\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\n  | github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\n  | github.com/milvus-io/milvus/internal/util/retry.Do\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\n  | github.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).init\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:168\n  | github.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:102\n  | github.com/milvus-io/milvus/cmd/components.(*QueryCoord).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/query_coord.go:52\n  | github.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123\n  | runtime.goexit\n  | \tD:/a/_temp/msys64/mingw64/lib/go/src/runtime/asm_amd64.s:1571\nWraps: (2) stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:168 github.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).init\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:102 github.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).Run\nWraps: (3) rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\nError types: (1) *withstack.withStack (2) *errutil.withPrefix (3) *status.Error"]
[2025/08/07 15:32:07.809 +08:00] [WARN] [grpcclient/client.go:370] ["ClientBase Call grpc first call get error"] [role=rootcoord] [error="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:544 github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369 github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"] [errorVerbose="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\n(1) attached stack trace\n  -- stack trace:\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\n  | github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\n  | github.com/milvus-io/milvus/internal/util/retry.Do\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\n  | github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:544\n  | github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369\n  | github.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/proxy.go:51\n  | github.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123\n  | runtime.goexit\n  | \tD:/a/_temp/msys64/mingw64/lib/go/src/runtime/asm_amd64.s:1571\nWraps: (2) stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:544 github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369 github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\nWraps: (3) rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\nError types: (1) *withstack.withStack (2) *errutil.withPrefix (3) *status.Error"]
[2025/08/07 15:32:07.810 +08:00] [WARN] [grpcclient/client.go:370] ["ClientBase Call grpc first call get error"] [role=rootcoord] [error="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:279 github.com/milvus-io/milvus/internal/distributed/datanode.(*Server).init\nD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:191 github.com/milvus-io/milvus/internal/distributed/datanode.(*Server).Run: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"] [errorVerbose="stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace: rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\n(1) attached stack trace\n  -- stack trace:\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\n  | github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\n  | github.com/milvus-io/milvus/internal/util/retry.Do\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\n  | github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\n  | github.com/milvus-io/milvus/internal/distributed/datanode.(*Server).init\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:279\n  | github.com/milvus-io/milvus/internal/distributed/datanode.(*Server).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:191\n  | github.com/milvus-io/milvus/cmd/components.(*DataNode).Run\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/data_node.go:52\n  | github.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n  | \tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123\n  | runtime.goexit\n  | \tD:/a/_temp/msys64/mingw64/lib/go/src/runtime/asm_amd64.s:1571\nWraps: (2) stack trace: D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/trace/stack_trace.go:51 github.com/milvus-io/milvus/internal/util/trace.StackTrace\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:369 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382 github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132 github.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39 github.com/milvus-io/milvus/internal/util/retry.Do\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114 github.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:279 github.com/milvus-io/milvus/internal/distributed/datanode.(*Server).init\n  | D:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:191 github.com/milvus-io/milvus/internal/distributed/datanode.(*Server).Run\nWraps: (3) rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch\nError types: (1) *withstack.withStack (2) *errutil.withPrefix (3) *status.Error"]
[2025/08/07 15:32:07.810 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.810 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.810 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.810 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.810 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.810 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:07.920 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:07.920 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:07.920 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:07.920 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:07.920 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:07.920 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.013 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:32:08.013 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:32:08.013 +08:00] [WARN] [grpcclient/client.go:298] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:32:08.013 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:32:08.013 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:32:08.013 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:32:08.013 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:32:08.013 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:32:08.013 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:32:08.013 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:32:08.013 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:32:08.030 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.030 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.030 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.030 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.030 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.030 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.154 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.154 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.154 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.154 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.154 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.154 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.263 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.263 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.263 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.263 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.263 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.263 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.373 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.373 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.373 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.373 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.373 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.373 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.481 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.481 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.481 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.481 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.481 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.481 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.590 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.591 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.591 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.591 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.591 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.591 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.696 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.696 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.696 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.696 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.696 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.696 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.805 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.805 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.805 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.805 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.805 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.805 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:08.916 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:08.916 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:32:08.916 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:08.916 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:32:08.916 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:08.916 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.026 +08:00] [INFO] [datanode/service.go:283] ["RootCoord client is ready for DataNode"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [datanode/service.go:291] ["starting DataCoord client for DataNode"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:32:09.026 +08:00] [INFO] [proxy/service.go:548] ["Proxy wait for RootCoord to be healthy done"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [proxy/service.go:551] ["set RootCoord client for Proxy done"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [querycoord/service.go:177] ["QueryCoord report RootCoord ready"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [proxy/service.go:555] ["create DataCoord client for Proxy"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [querycoord/service.go:196] ["QueryCoord try to wait for DataCoord ready"]
[2025/08/07 15:32:09.026 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.027 +08:00] [INFO] [proxy/service.go:561] ["create DataCoord client for Proxy done"]
[2025/08/07 15:32:09.027 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:32:09.027 +08:00] [INFO] [proxy/service.go:564] ["init DataCoord client for Proxy"]
[2025/08/07 15:32:09.027 +08:00] [INFO] [proxy/service.go:569] ["Proxy wait for DataCoord to be healthy"]
[2025/08/07 15:32:09.027 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.027 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:32:09.027 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.027 +08:00] [INFO] [indexcoord/service.go:179] ["IndexCoord try to wait for DataCoord ready"]
[2025/08/07 15:32:09.027 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:32:09.028 +08:00] [INFO] [datanode/service.go:309] ["DataCoord client is ready for DataNode"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [datanode/data_node.go:227] ["DataNode server initializing"] [TimeTickChannelName=by-dev-datacoord-timetick-channel]
[2025/08/07 15:32:09.028 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [querycoord/service.go:205] ["QueryCoord report DataCoord ready"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [indexcoord/service.go:88] ["IndexCoord init done ..."]
[2025/08/07 15:32:09.028 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=indexcoord] [ServerID=11]
[2025/08/07 15:32:09.028 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [proxy/service.go:574] ["Proxy wait for DataCoord to be healthy done"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [querycoord/service.go:226] ["QueryCoord try to wait for IndexCoord ready"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [proxy/service.go:577] ["set DataCoord client for Proxy done"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [proxy/service.go:581] ["create IndexCoord client for Proxy"]
[2025/08/07 15:32:09.028 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.030 +08:00] [INFO] [client/client.go:108] ["IndexCoordClient GetSessions success"] [key=indexcoord] [msess={}]
[2025/08/07 15:32:09.030 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.030 +08:00] [WARN] [client/client.go:111] ["IndexCoordClient msess key not existed"] [key=indexcoord] ["len of msess"=0]
[2025/08/07 15:32:09.030 +08:00] [INFO] [proxy/service.go:587] ["create IndexCoord client for Proxy done"]
[2025/08/07 15:32:09.030 +08:00] [INFO] [proxy/service.go:590] ["init IndexCoord client for Proxy"]
[2025/08/07 15:32:09.030 +08:00] [INFO] [proxy/service.go:595] ["init IndexCoord client for Proxy done"]
[2025/08/07 15:32:09.030 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available indexcoord, check indexcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/indexcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/client/client.go:126\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:227\ngithub.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:102\ngithub.com/milvus-io/milvus/cmd/components.(*QueryCoord).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/query_coord.go:52\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:32:09.030 +08:00] [WARN] [grpcclient/client.go:274] ["fail to get grpc client"] [client_role=indexcoord] [error="find no available indexcoord, check indexcoord state"]
[2025/08/07 15:32:09.030 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=indexcoord]
[2025/08/07 15:32:09.031 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/indexcoord] [value="{\"ServerID\":11,\"ServerName\":\"indexcoord\",\"Address\":\"**************:40003\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=indexcoord] [serverID=11]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/index_coord.go:142] ["IndexCoord Register Finished"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=14]
[2025/08/07 15:32:09.031 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=indexcoord] [key=indexcoord] [address=**************:40003]
[2025/08/07 15:32:09.031 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=indexcoord] [key=indexcoord] [address=**************:40003]
[2025/08/07 15:32:09.031 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:32:09.031 +08:00] [INFO] [client/client.go:108] ["IndexCoordClient GetSessions success"] [key=indexcoord] [msess="{\"indexcoord\":{\"ServerID\":11,\"ServerName\":\"indexcoord\",\"Address\":\"**************:40003\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}}"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [client/client.go:108] ["IndexCoordClient GetSessions success"] [key=indexcoord] [msess="{\"indexcoord\":{\"ServerID\":11,\"ServerName\":\"indexcoord\",\"Address\":\"**************:40003\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}}"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/service.go:199] ["IndexCoord registers service successfully"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/index_coord.go:1190] ["IndexCoord watchNodeLoop SessionAddEvent"] [serverID=10] [address=**************:40004]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/index_coord.go:1205] ["IndexCoord watchNodeLoop SessionDelEvent"] [serverID=1]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/index_coord.go:1333] ["IndexCoord start watching flushed segments..."]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/node_manager.go:95] ["IndexCoord addNode"] [nodeID=10] ["node address"=**************:40004]
[2025/08/07 15:32:09.031 +08:00] [DEBUG] [indexcoord/node_manager.go:59] ["IndexCoord NodeManager setClient"] [nodeID=10]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/node_manager.go:69] ["IndexNode NodeManager setClient success"] [nodeID=10] ["IndexNode num"=2]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/node_manager.go:76] [IndexCoord] ["Remove node with ID"=1]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/garbage_collector.go:81] ["IndexCoord garbageCollector recycleUnusedIndexes start"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/garbage_collector.go:196] ["IndexCoord garbageCollector recycleUnusedSegIndexes start"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/index_builder.go:141] ["index builder schedule loop start"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/garbage_collector.go:215] ["IndexCoord garbageCollector start recycleUnusedIndexFiles loop"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [indexcoord/flush_segment_watcher.go:145] ["IndexCoord flushedSegmentWatcher internalScheduler start..."]
[2025/08/07 15:32:09.031 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="datanode-14\n"] [filePath="\\tmp\\milvus\\server_id_28564"]
[2025/08/07 15:32:09.031 +08:00] [INFO] [datanode/data_node.go:240] ["DataNode server init rateCollector done"] ["node ID"=0]
[2025/08/07 15:32:09.032 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="indexcoord-11\n"] [filePath="\\tmp\\milvus\\server_id_28564"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [datanode/data_node.go:252] ["DataNode server init succeeded"] [MsgChannelSubName=by-dev-dataNode]
[2025/08/07 15:32:09.032 +08:00] [INFO] [indexcoord/index_coord.go:290] ["IndexCoord start successfully"] [state=Healthy]
[2025/08/07 15:32:09.032 +08:00] [INFO] [datanode/service.go:321] ["current DataNode state"] [state=Initializing]
[2025/08/07 15:32:09.032 +08:00] [INFO] [indexcoord/service.go:204] ["indexCoord started"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [datanode/service.go:195] ["DataNode gRPC services successfully initialized"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [indexcoord/service.go:92] ["IndexCoord start done ..."]
[2025/08/07 15:32:09.032 +08:00] [INFO] [datanode/data_node.go:480] ["start id allocator done"] [role=datanode]
[2025/08/07 15:32:09.032 +08:00] [DEBUG] [components/index_coord.go:55] ["IndexCoord successfully started"]
[2025/08/07 15:32:09.032 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.032 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.032 +08:00] [INFO] [proxy/service.go:601] ["Proxy wait for IndexCoord to be healthy done"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [proxy/service.go:604] ["set IndexCoord client for Proxy done"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [proxy/service.go:608] ["create QueryCoord client for Proxy"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [datanode/data_node.go:462] ["DataNode Background GC Start"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=datanode] [ServerID=14]
[2025/08/07 15:32:09.032 +08:00] [INFO] [proxy/service.go:614] ["create QueryCoord client for Proxy done"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [proxy/service.go:617] ["init QueryCoord client for Proxy"]
[2025/08/07 15:32:09.032 +08:00] [INFO] [proxy/service.go:622] ["init QueryCoord client for Proxy done"]
[2025/08/07 15:32:09.033 +08:00] [WARN] [client/client.go:94] ["QueryCoordClient msess key not existed"] [key=querycoord]
[2025/08/07 15:32:09.033 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available querycoord, check querycoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/client/client.go:123\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:624\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369\ngithub.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/proxy.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:32:09.033 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/datanode-14] [value="{\"ServerID\":14,\"ServerName\":\"datanode\",\"Address\":\"**************:40006\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:32:09.033 +08:00] [INFO] [sessionutil/session_util.go:690] ["watch services"] ["add kv"="key:\"by-dev/meta/session/datanode-14\" create_revision:55 mod_revision:55 version:1 value:\"{\\\"ServerID\\\":14,\\\"ServerName\\\":\\\"datanode\\\",\\\"Address\\\":\\\"**************:40006\\\",\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637499159507 "]
[2025/08/07 15:32:09.033 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=datanode] [serverID=14]
[2025/08/07 15:32:09.033 +08:00] [WARN] [client/client.go:94] ["QueryCoordClient msess key not existed"] [key=querycoord]
[2025/08/07 15:32:09.033 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionAddEvent]
[2025/08/07 15:32:09.033 +08:00] [INFO] [datanode/data_node.go:183] ["DataNode Register Finished"]
[2025/08/07 15:32:09.033 +08:00] [INFO] [datacoord/server.go:784] ["received datanode register"] [address=**************:40006] [serverID=14]
[2025/08/07 15:32:09.033 +08:00] [INFO] [datacoord/channel_manager.go:341] ["register node with no reassignment"] ["registered node"=14]
[2025/08/07 15:32:09.033 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available querycoord, check querycoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/client/client.go:123\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:624\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369\ngithub.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/proxy.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:32:09.033 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:32:09.033 +08:00] [INFO] [datanode/service.go:200] ["DataNode gRPC services successfully started"]
[2025/08/07 15:32:09.033 +08:00] [DEBUG] [components/data_node.go:56] ["Datanode successfully started"]
[2025/08/07 15:32:09.041 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.041 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.041 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.041 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.041 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.041 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.088 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=datanode] [key=by-dev/meta/session/datanode-14]
[2025/08/07 15:32:09.150 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.150 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.150 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.150 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.150 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.150 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.261 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.261 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.261 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.261 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.261 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.261 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.371 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.371 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.371 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.371 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.371 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.371 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.478 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.478 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.478 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.478 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.478 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.478 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.588 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.588 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.588 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.588 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.588 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.588 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.698 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.698 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.698 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.698 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.698 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.698 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.808 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.808 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.808 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.808 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.808 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.808 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.916 +08:00] [WARN] [rootcoord/proxy_client_manager.go:247] ["proxy client is empty, GetMetrics will not send to any client"]
[2025/08/07 15:32:09.916 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:32:09.916 +08:00] [WARN] [client/client.go:94] ["QueryCoordClient msess key not existed"] [key=querycoord]
[2025/08/07 15:32:09.916 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available querycoord, check querycoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetMetrics\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/client/client.go:361\ngithub.com/milvus-io/milvus/internal/rootcoord.(*QuotaCenter).syncMetrics.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/rootcoord/quota_center.go:188\ngolang.org/x/sync/errgroup.(*Group).Go.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.1.0/errgroup/errgroup.go:75"]
[2025/08/07 15:32:09.916 +08:00] [WARN] [grpcclient/client.go:274] ["fail to get grpc client"] [client_role=querycoord] [error="find no available querycoord, check querycoord state"]
[2025/08/07 15:32:09.916 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=querycoord]
[2025/08/07 15:32:09.916 +08:00] [WARN] [client/client.go:94] ["QueryCoordClient msess key not existed"] [key=querycoord]
[2025/08/07 15:32:09.916 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available querycoord, check querycoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetMetrics\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/client/client.go:361\ngithub.com/milvus-io/milvus/internal/rootcoord.(*QuotaCenter).syncMetrics.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/rootcoord/quota_center.go:188\ngolang.org/x/sync/errgroup.(*Group).Go.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.1.0/errgroup/errgroup.go:75"]
[2025/08/07 15:32:09.916 +08:00] [WARN] [grpcclient/client.go:281] ["fail to get grpc client in the retry state"] [client_role=querycoord] [error="find no available querycoord, check querycoord state"]
[2025/08/07 15:32:09.916 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:09.916 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:09.916 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:09.916 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:09.916 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:09.916 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:09.920 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=datanode] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=datanode] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=datanode] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=datanode] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=datanode] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=datanode] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=datanode] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:32:09.922 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=datanode] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:32:10.025 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.025 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.025 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.025 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.025 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.025 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.039 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.039 +08:00] [WARN] [client/client.go:94] ["QueryCoordClient msess key not existed"] [key=querycoord]
[2025/08/07 15:32:10.039 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.039 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available querycoord, check querycoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/client/client.go:123\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:624\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369\ngithub.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/proxy.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:32:10.039 +08:00] [INFO] [querycoord/service.go:232] ["QueryCoord report IndexCoord is ready"]
[2025/08/07 15:32:10.039 +08:00] [INFO] [querycoordv2/server.go:176] ["QueryCoord start init"] [meta-root-path=by-dev/meta] [address=**************:40001]
[2025/08/07 15:32:10.039 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:32:10.039 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:32:10.041 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=15]
[2025/08/07 15:32:10.041 +08:00] [INFO] [querycoordv2/server.go:209] [QueryCoord] [State=Initializing]
[2025/08/07 15:32:10.041 +08:00] [INFO] [querycoordv2/server.go:214] ["query coordinator try to connect etcd success"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [tso/tso.go:121] ["sync and save timestamp"] [last=2025/08/07 15:29:22.165 +08:00] [save=2025/08/07 15:32:13.041 +08:00] [next=2025/08/07 15:32:10.041 +08:00]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:302] ["init meta"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:312] ["recover meta..."]
[2025/08/07 15:32:10.042 +08:00] [INFO] [meta/collection_manager.go:89] ["recover collections and partitions from kv store"] [traceID=1754551930042094100]
[2025/08/07 15:32:10.042 +08:00] [INFO] [meta/resource_manager.go:672] ["Recover resource group"] [rgName=__default_resource_group] [nodes="[2]"] [capacity=1000000]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:238] ["init session"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:242] ["init schedulers"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:255] ["init dist controller"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:265] ["init all available balancer"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:274] ["use config balancer"] [balancer=ScoreBasedBalancer]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:281] ["init checker controller"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:345] ["init observers"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoordv2/server.go:297] ["QueryCoord init success"]
[2025/08/07 15:32:10.042 +08:00] [INFO] [querycoord/service.go:105] ["QueryCoord init done ..."]
[2025/08/07 15:32:10.042 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=querycoord] [ServerID=15]
[2025/08/07 15:32:10.044 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/querycoord] [value="{\"ServerID\":15,\"ServerName\":\"querycoord\",\"Address\":\"**************:40001\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:32:10.044 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=querycoord] [serverID=15]
[2025/08/07 15:32:10.044 +08:00] [INFO] [querycoordv2/server.go:144] ["QueryCoord Register Finished"]
[2025/08/07 15:32:10.044 +08:00] [INFO] [sessionutil/session_util.go:690] ["watch services"] ["add kv"="key:\"by-dev/meta/session/querycoord\" create_revision:59 mod_revision:59 version:1 value:\"{\\\"ServerID\\\":15,\\\"ServerName\\\":\\\"querycoord\\\",\\\"Address\\\":\\\"**************:40001\\\",\\\"Exclusive\\\":true,\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637499159532 "]
[2025/08/07 15:32:10.044 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionAddEvent]
[2025/08/07 15:32:10.044 +08:00] [INFO] [datacoord/server.go:713] ["there is a new service online"] ["server role"=QueryCoord] ["server ID"=15]
[2025/08/07 15:32:10.044 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:32:10.044 +08:00] [INFO] [querycoordv2/server.go:388] ["start watcher..."]
[2025/08/07 15:32:10.044 +08:00] [INFO] [sessionutil/session_util.go:561] ["SessionUtil GetSessions "] [prefix=querynode] [key=querynode-9] [address=**************:40002]
[2025/08/07 15:32:10.044 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=querynode] [key=querynode-9] [address=**************:40002]
[2025/08/07 15:32:10.044 +08:00] [INFO] [task/scheduler.go:226] ["add executor for new QueryNode"] [nodeID=9]
[2025/08/07 15:32:10.044 +08:00] [INFO] [dist/dist_handler.go:59] ["start dist handler"] [nodeID=9]
[2025/08/07 15:32:10.044 +08:00] [INFO] [meta/resource_manager.go:468] ["HandleNodeUp: add node to default resource group"] [rgName=__default_resource_group] [node=9]
[2025/08/07 15:32:10.044 +08:00] [INFO] [querycoordv2/server.go:726] ["HandleNodeUp: assign node to resource group"] [nodeID=9] [resourceGroup=__default_resource_group]
[2025/08/07 15:32:10.044 +08:00] [INFO] [querycoordv2/server.go:410] ["start recovering dist and target"]
[2025/08/07 15:32:10.048 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=querynode] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=querynode] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=querynode] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=querynode] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=querynode] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=querynode] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=querynode] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:32:10.050 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=querynode] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:32:10.051 +08:00] [INFO] [querycoordv2/server.go:423] ["start cluster..."]
[2025/08/07 15:32:10.051 +08:00] [INFO] [querycoordv2/server.go:426] ["start job scheduler..."]
[2025/08/07 15:32:10.051 +08:00] [INFO] [querycoordv2/server.go:429] ["start task scheduler..."]
[2025/08/07 15:32:10.051 +08:00] [INFO] [querycoordv2/server.go:432] ["start checker controller..."]
[2025/08/07 15:32:10.051 +08:00] [INFO] [querycoordv2/server.go:435] ["start observers..."]
[2025/08/07 15:32:10.051 +08:00] [INFO] [observers/target_observer.go:96] ["Start update next target loop"]
[2025/08/07 15:32:10.051 +08:00] [INFO] [observers/replica_observer.go:64] ["Start check replica loop"]
[2025/08/07 15:32:10.051 +08:00] [INFO] [observers/resource_observer.go:61] ["Start check resource group loop"]
[2025/08/07 15:32:10.052 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="querycoord-15\n"] [filePath="\\tmp\\milvus\\server_id_28564"]
[2025/08/07 15:32:10.052 +08:00] [INFO] [querycoordv2/server.go:382] ["QueryCoord started"]
[2025/08/07 15:32:10.052 +08:00] [INFO] [querycoord/service.go:110] ["QueryCoord start done ..."]
[2025/08/07 15:32:10.052 +08:00] [DEBUG] [components/query_coord.go:56] ["QueryCoord successfully started"]
[2025/08/07 15:32:10.071 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=querycoord] [key=by-dev/meta/session/querycoord]
[2025/08/07 15:32:10.133 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.133 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.133 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.133 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.133 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.133 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.244 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.244 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.244 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.244 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.244 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.244 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.353 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.353 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.353 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.353 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.353 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.353 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.461 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.461 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.461 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.461 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.461 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.461 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.572 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.572 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.572 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.572 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.572 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.572 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.680 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.680 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.680 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.680 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.680 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.680 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.792 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.792 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.792 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.792 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.792 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.792 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.901 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:10.901 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:10.901 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:10.901 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:10.901 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:10.901 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:10.931 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=querycoord]
[2025/08/07 15:32:10.931 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=querycoord] [key=querycoord] [address=**************:40001]
[2025/08/07 15:32:11.010 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:11.010 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:11.010 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:11.010 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:11.010 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:11.010 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:32:11.118 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:32:11.118 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:11 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:32:11.118 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:32:11.118 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:32:11.118 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:32:11.118 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:10 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]

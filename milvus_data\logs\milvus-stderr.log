2025/08/07 15:31:41 maxprocs: Leaving GOMAXPROCS=12: CPU quota undefined

    __  _________ _   ____  ______    
   /  |/  /  _/ /| | / / / / / __/    
  / /|_/ // // /_| |/ / /_/ /\ \    
 /_/  /_/___/____/___/\____/___/     

Welcome to use Milvus!
Version:   v2.2.16-lite
Built:     Sun, Dec  3, 2023 11:20:00 AM
GitCommit: af56b2843
GoVersion: go version go1.18 windows/amd64

open pid file: run/standalone.pid
lock pid file: run/standalone.pid
ASSERTION FAILURE FROM EASYLOGGING++ (LINE: 306) [(assertionPassed = base::utils::File::pathExists(configurationFile.c_str(), true)) == true] WITH MESSAGE "Configuration file [D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml] does not exist!"
ASSERTION FAILURE FROM EASYLOGGING++ (LINE: 306) [(assertionPassed = base::utils::File::pathExists(configurationFile.c_str(), true)) == true] WITH MESSAGE "Configuration file [D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml] does not exist!"

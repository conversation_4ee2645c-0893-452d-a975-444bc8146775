[2025/08/07 15:29:15.933 +08:00] [INFO] [logutil/logutil.go:165] ["Log directory"] [configDir="D:\\AAAAA-wq-work\\access\\milvus_data\\logs"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [logutil/logutil.go:166] ["Set log file to "] [path="D:\\AAAAA-wq-work\\access\\milvus_data\\logs/standalone-2.log"]
[2025/08/07 15:29:15.933 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=datacoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=rootcoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="indexnode-1\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=rootcoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=datacoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/index_coord.go:207] ["IndexCoord init"] [stateCode=Initializing]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=datacoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=rootcoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [indexnode/indexnode.go:204] ["IndexNode init session successful"] [serverID=1]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=rootcoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=rootcoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/index_coord.go:214] ["IndexCoord try to connect etcd"]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=rootcoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=rootcoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [indexnode/indexnode.go:212] ["IndexNode NewMinIOKV succeeded"]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=rootcoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="querynode-2\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/meta_table.go:98] ["IndexCoord metaTable reloadFromKV load indexes"]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=datacoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=datacoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [querycoord/service.go:167] ["QueryCoord try to wait for RootCoord ready"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [querynode/query_node.go:211] ["QueryNode init session"] [nodeID=2] ["node address"=**************:40002]
[2025/08/07 15:29:15.934 +08:00] [INFO] [proxy/service.go:533] ["create RootCoord client for Proxy done"]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=datacoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [proxy/service.go:536] ["init RootCoord client for Proxy"]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=datacoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [proxy/service.go:541] ["init RootCoord client for Proxy done"]
[2025/08/07 15:29:15.934 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=datacoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:15.934 +08:00] [INFO] [querynode/query_node.go:327] ["QueryNode init rateCollector done"] [nodeID=2]
[2025/08/07 15:29:15.934 +08:00] [INFO] [rootcoord/service.go:213] ["RootCoord start to create IndexCoord client"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/meta_table.go:118] ["IndexCoord metaTable reloadFromKV success"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [querynode/query_node.go:337] ["queryNode try to connect etcd success"] [MetaRootPath=by-dev/meta]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/index_coord.go:221] ["IndexCoord try to connect etcd success"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [proxy/service.go:543] ["Proxy wait for RootCoord to be healthy"]
[2025/08/07 15:29:15.934 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/index_coord.go:225] [IndexCoord] ["session number"=0] [revision=5]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/index_coord.go:230] ["IndexCoord get node sessions from etcd"] ["bind mode"=false] ["node address"=localhost:22930]
[2025/08/07 15:29:15.934 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:15.934 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/index_coord.go:253] [IndexCoord] ["IndexNode number"=0]
[2025/08/07 15:29:15.934 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.934 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:168\ngithub.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:102\ngithub.com/milvus-io/milvus/cmd/components.(*QueryCoord).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/query_coord.go:52\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.934 +08:00] [WARN] [grpcclient/client.go:274] ["fail to get grpc client"] [client_role=rootcoord] [error="find no available rootcoord, check rootcoord state"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [querynode/segment_loader.go:978] ["SegmentLoader created"] [ioPoolSize=96] [cpuPoolSize=12]
[2025/08/07 15:29:15.934 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/datanode.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:279\ngithub.com/milvus-io/milvus/internal/distributed/datanode.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:191\ngithub.com/milvus-io/milvus/cmd/components.(*DataNode).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/data_node.go:52\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.934 +08:00] [WARN] [grpcclient/client.go:274] ["fail to get grpc client"] [client_role=rootcoord] [error="find no available rootcoord, check rootcoord state"]
[2025/08/07 15:29:15.934 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=rootcoord]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/index_coord.go:264] ["IndexCoord new minio chunkManager success"]
[2025/08/07 15:29:15.934 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=rootcoord]
[2025/08/07 15:29:15.934 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:544\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369\ngithub.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/proxy.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.934 +08:00] [INFO] [indexcoord/flush_segment_watcher.go:87] ["flushSegmentWatcher reloadFromKV"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.935 +08:00] [INFO] [indexcoord/flush_segment_watcher.go:109] ["flushSegmentWatcher reloadFromKV success"] [etcdRevision=5]
[2025/08/07 15:29:15.935 +08:00] [INFO] [indexcoord/index_coord.go:278] ["IndexCoord new task scheduler success"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.935 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.935 +08:00] [INFO] [indexcoord/index_coord.go:281] ["IndexCoord init finished"]
[2025/08/07 15:29:15.935 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:15.935 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:168\ngithub.com/milvus-io/milvus/internal/distributed/querycoord.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/service.go:102\ngithub.com/milvus-io/milvus/cmd/components.(*QueryCoord).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/query_coord.go:52\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.935 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:544\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369\ngithub.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/proxy.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [grpcclient/client.go:281] ["fail to get grpc client in the retry state"] [client_role=rootcoord] [error="find no available rootcoord, check rootcoord state"]
[2025/08/07 15:29:15.935 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:15.935 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/datanode.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:279\ngithub.com/milvus-io/milvus/internal/distributed/datanode.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/datanode/service.go:191\ngithub.com/milvus-io/milvus/cmd/components.(*DataNode).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/data_node.go:52\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.935 +08:00] [INFO] [indexcoord/service.go:151] ["IndexCoord try to wait for RootCoord ready"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [grpcclient/client.go:281] ["fail to get grpc client in the retry state"] [client_role=rootcoord] [error="find no available rootcoord, check rootcoord state"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.935 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:152\ngithub.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:85\ngithub.com/milvus-io/milvus/cmd/components.(*IndexCoord).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/index_coord.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [grpcclient/client.go:274] ["fail to get grpc client"] [client_role=rootcoord] [error="find no available rootcoord, check rootcoord state"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=rootcoord]
[2025/08/07 15:29:15.935 +08:00] [WARN] [client/client.go:106] ["RootCoordClient mess key not exist"] [key=rootcoord]
[2025/08/07 15:29:15.935 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available rootcoord, check rootcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/rootcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/rootcoord/client/client.go:132\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:152\ngithub.com/milvus-io/milvus/internal/distributed/indexcoord.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/service.go:85\ngithub.com/milvus-io/milvus/cmd/components.(*IndexCoord).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/index_coord.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:15.935 +08:00] [WARN] [grpcclient/client.go:281] ["fail to get grpc client in the retry state"] [client_role=rootcoord] [error="find no available rootcoord, check rootcoord state"]
[2025/08/07 15:29:15.935 +08:00] [DEBUG] [indexnode/indexnode.go:218] ["Init IndexNode finished"] []
[2025/08/07 15:29:15.935 +08:00] [INFO] [indexnode/service.go:78] ["IndexNode init done ..."]
[2025/08/07 15:29:15.935 +08:00] [DEBUG] [indexnode/indexnode.go:233] [IndexNode] [State=Healthy]
[2025/08/07 15:29:15.935 +08:00] [DEBUG] [indexnode/indexnode.go:236] ["IndexNode start finished"] []
[2025/08/07 15:29:15.935 +08:00] [INFO] [indexnode/task_scheduler.go:240] ["IndexNode TaskScheduler start build loop ..."]
[2025/08/07 15:29:15.935 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=indexnode] [ServerID=1]
[2025/08/07 15:29:15.935 +08:00] [INFO] [gc/gc_tuner.go:130] ["GC Helper initialized."] ["Initial GoGC"=100] [minimumGOGC=30] [maximumGOGC=200] [memoryThreshold=38498125824]
[2025/08/07 15:29:15.935 +08:00] [INFO] [querynode/query_node.go:382] ["query node init successfully"] [queryNodeID=2] [IP=**************] [Port=40002]
[2025/08/07 15:29:15.935 +08:00] [INFO] [querynode/service.go:235] ["QueryNode init done ..."]
[2025/08/07 15:29:15.936 +08:00] [INFO] [querynode/query_node.go:411] ["query node start successfully"] [queryNodeID=2] [IP=**************] [Port=40002]
[2025/08/07 15:29:15.936 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=querynode] [ServerID=2]
[2025/08/07 15:29:15.937 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/indexnode-1] [value="{\"ServerID\":1,\"ServerName\":\"indexnode\",\"Address\":\"**************:40004\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:15.937 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=indexnode] [serverID=1]
[2025/08/07 15:29:15.937 +08:00] [INFO] [sessionutil/session_util.go:690] ["watch services"] ["add kv"="key:\"by-dev/meta/session/indexnode-1\" create_revision:6 mod_revision:6 version:1 value:\"{\\\"ServerID\\\":1,\\\"ServerName\\\":\\\"indexnode\\\",\\\"Address\\\":\\\"**************:40004\\\",\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637461779233 "]
[2025/08/07 15:29:15.937 +08:00] [INFO] [indexnode/indexnode.go:133] ["IndexNode Register Finished"]
[2025/08/07 15:29:15.937 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionAddEvent]
[2025/08/07 15:29:15.937 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/querynode-2] [value="{\"ServerID\":2,\"ServerName\":\"querynode\",\"Address\":\"**************:40002\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:15.937 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=querynode] [serverID=2]
[2025/08/07 15:29:15.937 +08:00] [INFO] [querynode/query_node.go:219] ["QueryNode Register Finished"]
[2025/08/07 15:29:15.937 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:15.938 +08:00] [INFO] [querynode/service.go:240] ["QueryNode start done ..."]
[2025/08/07 15:29:15.938 +08:00] [DEBUG] [components/query_node.go:57] ["QueryNode successfully started"]
[2025/08/07 15:29:15.938 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:15.938 +08:00] [INFO] [indexnode/service.go:214] ["IndexNode Register etcd success"]
[2025/08/07 15:29:15.938 +08:00] [INFO] [indexnode/service.go:82] ["IndexNode start done ..."]
[2025/08/07 15:29:15.938 +08:00] [DEBUG] [components/index_node.go:55] ["IndexNode successfully started"]
[2025/08/07 15:29:15.939 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=indexcoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=indexcoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=indexcoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=indexcoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=indexcoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=indexcoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=indexcoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:15.942 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=indexcoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:15.942 +08:00] [INFO] [rootcoord/service.go:221] ["RootCoord start to create QueryCoord client"]
[2025/08/07 15:29:15.942 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:15.942 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:15.946 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=querycoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=querycoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=querycoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=querycoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=querycoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=querycoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=querycoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:15.948 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=querycoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:15.948 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:15.948 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:15.950 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=4]
[2025/08/07 15:29:15.950 +08:00] [INFO] [rootcoord/root_coord.go:173] ["update rootcoord state"] [state=Initializing]
[2025/08/07 15:29:15.950 +08:00] [INFO] [tso/tso.go:121] ["sync and save timestamp"] [last=0001/01/01 00:00:00.000 +00:00] [save=2025/08/07 15:29:18.950 +08:00] [next=2025/08/07 15:29:15.950 +08:00]
[2025/08/07 15:29:15.950 +08:00] [INFO] [rootcoord/root_coord.go:385] ["id allocator initialized"] [root_path=by-dev/kv] [sub_path=gid] [key=idTimestamp]
[2025/08/07 15:29:15.951 +08:00] [INFO] [tso/tso.go:121] ["sync and save timestamp"] [last=0001/01/01 00:00:00.000 +00:00] [save=2025/08/07 15:29:18.951 +08:00] [next=2025/08/07 15:29:15.951 +08:00]
[2025/08/07 15:29:15.951 +08:00] [INFO] [rootcoord/root_coord.go:401] ["tso allocator initialized"] [root_path=by-dev/kv] [sub_path=gid] [key=idTimestamp]
[2025/08/07 15:29:15.951 +08:00] [DEBUG] [rootcoord/suffix_snapshot.go:558] ["suffix snapshot GC goroutine start!"]
[2025/08/07 15:29:15.951 +08:00] [INFO] [rootcoord/meta_table.go:148] ["recover databases"] ["num of dbs"=0]
[2025/08/07 15:29:15.952 +08:00] [INFO] [rootcoord/meta_table.go:271] ["create database"] [db=default] [ts=459945215512018945]
[2025/08/07 15:29:15.952 +08:00] [INFO] [rootcoord/meta_table.go:186] ["recover collections from db"] [collection_num=0] [partition_num=0]
[2025/08/07 15:29:15.952 +08:00] [INFO] [rootcoord/meta_table.go:203] ["meta table recovery finished"]
[2025/08/07 15:29:15.952 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_0] [elapsed=0]
[2025/08/07 15:29:15.952 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_1] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_2] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_3] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_4] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_5] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_6] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_7] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_8] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_9] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_10] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_11] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_12] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_13] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_14] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [server/rocksmq_impl.go:384] ["Rocksmq create topic successfully "] [topic=by-dev-rootcoord-dml_15] [elapsed=0]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rmq/rmq_client.go:65] ["tr/create producer"] [msg="create producer done"] [duration=0s]
[2025/08/07 15:29:15.953 +08:00] [INFO] [rootcoord/dml_channels.go:178] ["init dml channels"] [prefix=by-dev-rootcoord-dml] [num=16]
[2025/08/07 15:29:15.953 +08:00] [INFO] [rootcoord/timeticksync.go:226] ["Add session for timeticksync"] [serverID=4]
[2025/08/07 15:29:15.953 +08:00] [INFO] [rootcoord/root_coord.go:470] ["RootCoord init QuotaCenter done"]
[2025/08/07 15:29:15.953 +08:00] [DEBUG] [rootcoord/kv_catalog.go:387] ["not found the user"] [key=root-coord/credential/users/root]
[2025/08/07 15:29:15.953 +08:00] [INFO] [rootcoord/root_coord.go:532] ["RootCoord init user root"]
[2025/08/07 15:29:15.954 +08:00] [DEBUG] [rootcoord/kv_catalog.go:387] ["not found the user"] [key=root-coord/credential/users/root]
[2025/08/07 15:29:15.955 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:29:15.955 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:29:15.955 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:29:15.956 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:29:15.956 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:29:15.956 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:29:15.956 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:29:15.956 +08:00] [DEBUG] [rootcoord/kv_catalog.go:965] ["not found the privilege entity"] [key=root-coord/credential/grantee-privileges/public/Global/*.*] [type=Grant]
[2025/08/07 15:29:15.957 +08:00] [DEBUG] [rootcoord/kv_catalog.go:994] ["not found the grantee id"] [key=root-coord/credential/grantee-id/250dd41b686083b0/PrivilegeDescribeCollection]
[2025/08/07 15:29:15.957 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:29:15.957 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:29:15.958 +08:00] [DEBUG] [rootcoord/kv_catalog.go:994] ["not found the grantee id"] [key=root-coord/credential/grantee-id/250dd41b686083b0/PrivilegeShowCollections]
[2025/08/07 15:29:15.958 +08:00] [DEBUG] [rootcoord/kv_catalog.go:965] ["not found the privilege entity"] [key=root-coord/credential/grantee-privileges/public/Collection/*.*] [type=Grant]
[2025/08/07 15:29:15.960 +08:00] [DEBUG] [rootcoord/kv_catalog.go:994] ["not found the grantee id"] [key=root-coord/credential/grantee-id/e03326696e8a3b16/PrivilegeIndexDetail]
[2025/08/07 15:29:15.960 +08:00] [INFO] [rootcoord/service.go:162] ["RootCoord init done ..."]
[2025/08/07 15:29:15.960 +08:00] [INFO] [rootcoord/service.go:299] ["RootCoord Core start ..."]
[2025/08/07 15:29:15.960 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=rootcoord] [ServerID=4]
[2025/08/07 15:29:15.961 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/rootcoord] [value="{\"ServerID\":4,\"ServerName\":\"rootcoord\",\"Address\":\"**************:40000\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:15.961 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=rootcoord] [serverID=4]
[2025/08/07 15:29:15.961 +08:00] [INFO] [rootcoord/root_coord.go:302] ["RootCoord Register Finished"]
[2025/08/07 15:29:15.961 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:15.961 +08:00] [INFO] [rootcoord/proxy_manager.go:85] ["succeed to init sessions on etcd"] [sessions=null] [revision=20]
[2025/08/07 15:29:15.965 +08:00] [INFO] [rootcoord/quota_center.go:132] ["Start QuotaCenter"] [collectInterval/s=3]
[2025/08/07 15:29:15.965 +08:00] [INFO] [rootcoord/proxy_manager.go:104] ["start to watch etcd"]
[2025/08/07 15:29:15.965 +08:00] [WARN] [rootcoord/proxy_client_manager.go:220] ["proxy client is empty, RefreshPrivilegeInfoCache will not send to any client"]
[2025/08/07 15:29:15.965 +08:00] [INFO] [rootcoord/root_coord.go:173] ["update rootcoord state"] [state=Healthy]
[2025/08/07 15:29:15.965 +08:00] [INFO] [gc/gc_tuner.go:84] ["GC Tune done"] ["previous GOGC"=100] ["heapuse "=32] ["total memory"=18265] ["next GC"=37] ["new GOGC"=200] [gc-pause=0s] [gc-pause-end=1754551755965264000]
[2025/08/07 15:29:15.965 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="rootcoord-4\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:15.965 +08:00] [INFO] [rootcoord/root_coord.go:676] ["rootcoord startup successfully"]
[2025/08/07 15:29:15.965 +08:00] [INFO] [rootcoord/service.go:167] ["RootCoord start done ..."]
[2025/08/07 15:29:15.965 +08:00] [DEBUG] [components/root_coord.go:60] ["RootCoord successfully started"]
[2025/08/07 15:29:15.973 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=querynode] [key=by-dev/meta/session/querynode-2]
[2025/08/07 15:29:15.973 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=rootcoord] [key=by-dev/meta/session/rootcoord]
[2025/08/07 15:29:15.996 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:15.996 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:15.996 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:15.996 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:15.996 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:15.996 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.122 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:16.122 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:16.122 +08:00] [INFO] [datacoord/service.go:123] ["create IndexCoord client for DataCoord done"]
[2025/08/07 15:29:16.122 +08:00] [INFO] [datacoord/service.go:126] ["init IndexCoord client for DataCoord"]
[2025/08/07 15:29:16.122 +08:00] [INFO] [datacoord/service.go:131] ["init IndexCoord client for DataCoord done"]
[2025/08/07 15:29:16.122 +08:00] [INFO] [datacoord/service.go:159] ["network port"] [port=40005]
[2025/08/07 15:29:16.122 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.122 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.122 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.122 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.122 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.122 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.169 +08:00] [WARN] [rootcoord/root_coord.go:208] ["zero ts was met, this should be only occurred in starting state"] [minBgDdlTs=459945215556845569] [minNormalDdlTs=0]
[2025/08/07 15:29:16.230 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:16.230 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:16.231 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.231 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.231 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.231 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.231 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.231 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.232 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=5]
[2025/08/07 15:29:16.232 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:16.232 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:16.233 +08:00] [INFO] [datacoord/server.go:439] ["DataCoord success to get DataNode sessions"] [sessions={}]
[2025/08/07 15:29:16.233 +08:00] [INFO] [datacoord/channel_manager.go:171] ["starting etcd states checker"]
[2025/08/07 15:29:16.233 +08:00] [INFO] [datacoord/channel_manager.go:176] ["starting background balance checker"]
[2025/08/07 15:29:16.233 +08:00] [INFO] [datacoord/channel_manager.go:179] ["cluster start up"] [nodes="[]"] [oNodes="[]"] ["old onlines"="[]"] ["new onlines"="[]"] [offLines="[]"]
[2025/08/07 15:29:16.233 +08:00] [INFO] [datacoord/segment_reference_manager.go:41] ["create a new segment reference manager"]
[2025/08/07 15:29:16.233 +08:00] [INFO] [datacoord/segment_reference_manager.go:184] ["recovery reference lock on segments by online nodes"] ["online nodeIDs"="[]"]
[2025/08/07 15:29:16.234 +08:00] [INFO] [datacoord/segment_reference_manager.go:204] ["recovery reference lock on segments by online nodes successfully"] ["online nodeIDs"="[]"] ["offline nodeIDs"={}]
[2025/08/07 15:29:16.234 +08:00] [INFO] [datacoord/segment_reference_manager.go:75] ["create new segment reference manager successfully"]
[2025/08/07 15:29:16.234 +08:00] [INFO] [datacoord/garbage_collector.go:71] ["GC with option"] [enabled=true] [interval=1h0m0s] [missingTolerance=1h0m0s] [dropTolerance=3h0m0s]
[2025/08/07 15:29:16.234 +08:00] [INFO] [datacoord/service.go:264] ["DataCoord init done ..."]
[2025/08/07 15:29:16.234 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=datacoord] [ServerID=5]
[2025/08/07 15:29:16.235 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/datacoord] [value="{\"ServerID\":5,\"ServerName\":\"datacoord\",\"Address\":\"**************:40005\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:16.235 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=datacoord] [serverID=5]
[2025/08/07 15:29:16.235 +08:00] [INFO] [datacoord/server.go:242] ["DataCoord Register Finished"]
[2025/08/07 15:29:16.235 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:16.235 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="datacoord-5\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:16.235 +08:00] [INFO] [datacoord/server.go:351] ["DataCoord startup successfully"]
[2025/08/07 15:29:16.236 +08:00] [INFO] [datacoord/service.go:269] ["DataCoord start done ..."]
[2025/08/07 15:29:16.236 +08:00] [DEBUG] [components/data_coord.go:53] ["DataCoord successfully started"]
[2025/08/07 15:29:16.294 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=datacoord] [key=by-dev/meta/session/datacoord]
[2025/08/07 15:29:16.343 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.343 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.343 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.343 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.343 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.343 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.453 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.453 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.453 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.453 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.453 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.453 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.577 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.577 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.577 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.577 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.577 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.577 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.688 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.688 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.688 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.688 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.688 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.688 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.800 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.800 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.800 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.800 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.800 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.800 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.907 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:16.907 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:16.907 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:16.907 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:16.907 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:16.907 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:16.938 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=rootcoord]
[2025/08/07 15:29:16.938 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=rootcoord]
[2025/08/07 15:29:16.938 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=rootcoord]
[2025/08/07 15:29:16.938 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:29:16.938 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:29:16.938 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:29:16.938 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:29:16.938 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:29:16.938 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:29:16.938 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:29:16.938 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:29:17.016 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.016 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.016 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.016 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.016 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.016 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.126 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.126 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.126 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.126 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.126 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.126 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.237 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.237 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.237 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.237 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.237 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.237 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.347 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.347 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.347 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.347 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.347 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.347 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.457 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.457 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.457 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.457 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.457 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.457 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.567 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.567 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.567 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.567 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.567 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.567 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.678 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.678 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.678 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.678 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.678 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.678 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.802 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.803 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.803 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.803 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.803 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.803 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:17.914 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:17.914 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:17.914 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:17.914 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:17.914 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:17.914 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.025 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.025 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.025 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.025 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.025 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.025 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.135 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.135 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.135 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.135 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.135 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.135 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.243 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.243 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.243 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.243 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.243 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.243 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.353 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.353 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.353 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.353 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.353 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.353 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.462 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.462 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.463 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.463 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.463 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.463 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.571 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.571 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.571 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.571 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.571 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.571 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.681 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.681 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.681 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.681 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.681 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.682 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.788 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.788 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.788 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.788 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.788 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.788 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.900 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.900 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.900 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:18.900 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:29:18.900 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:18.900 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:18.946 +08:00] [INFO] [proxy/service.go:548] ["Proxy wait for RootCoord to be healthy done"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [proxy/service.go:551] ["set RootCoord client for Proxy done"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [proxy/service.go:555] ["create DataCoord client for Proxy"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [querycoord/service.go:177] ["QueryCoord report RootCoord ready"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [datanode/service.go:283] ["RootCoord client is ready for DataNode"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [datanode/service.go:291] ["starting DataCoord client for DataNode"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:18.946 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [proxy/service.go:561] ["create DataCoord client for Proxy done"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [proxy/service.go:564] ["init DataCoord client for Proxy"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [proxy/service.go:569] ["Proxy wait for DataCoord to be healthy"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:29:18.947 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:29:18.946 +08:00] [INFO] [indexcoord/service.go:179] ["IndexCoord try to wait for DataCoord ready"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [querycoord/service.go:196] ["QueryCoord try to wait for DataCoord ready"]
[2025/08/07 15:29:18.947 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:29:18.947 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:29:18.948 +08:00] [INFO] [datanode/service.go:309] ["DataCoord client is ready for DataNode"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [datanode/data_node.go:227] ["DataNode server initializing"] [TimeTickChannelName=by-dev-datacoord-timetick-channel]
[2025/08/07 15:29:18.948 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [proxy/service.go:574] ["Proxy wait for DataCoord to be healthy done"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [proxy/service.go:577] ["set DataCoord client for Proxy done"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [proxy/service.go:581] ["create IndexCoord client for Proxy"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [querycoord/service.go:205] ["QueryCoord report DataCoord ready"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:18.948 +08:00] [INFO] [indexcoord/service.go:88] ["IndexCoord init done ..."]
[2025/08/07 15:29:18.948 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=indexcoord] [ServerID=3]
[2025/08/07 15:29:18.949 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:18.949 +08:00] [INFO] [proxy/service.go:587] ["create IndexCoord client for Proxy done"]
[2025/08/07 15:29:18.949 +08:00] [INFO] [proxy/service.go:590] ["init IndexCoord client for Proxy"]
[2025/08/07 15:29:18.949 +08:00] [INFO] [proxy/service.go:595] ["init IndexCoord client for Proxy done"]
[2025/08/07 15:29:18.950 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:18.950 +08:00] [INFO] [querycoord/service.go:226] ["QueryCoord try to wait for IndexCoord ready"]
[2025/08/07 15:29:18.950 +08:00] [INFO] [client/client.go:108] ["IndexCoordClient GetSessions success"] [key=indexcoord] [msess={}]
[2025/08/07 15:29:18.950 +08:00] [WARN] [client/client.go:111] ["IndexCoordClient msess key not existed"] [key=indexcoord] ["len of msess"=0]
[2025/08/07 15:29:18.950 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available indexcoord, check indexcoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/indexcoord/client.(*Client).GetComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/indexcoord/client/client.go:126\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:75\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentStates\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:99\ngithub.com/milvus-io/milvus/internal/util/funcutil.WaitForComponentHealthy\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/funcutil/func.go:114\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:597\ngithub.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/proxy/service.go:369\ngithub.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/components/proxy.go:51\ngithub.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/cmd/roles/roles.go:123"]
[2025/08/07 15:29:18.950 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/indexcoord] [value="{\"ServerID\":3,\"ServerName\":\"indexcoord\",\"Address\":\"**************:40003\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=indexcoord] [key=indexcoord] [address=**************:40003]
[2025/08/07 15:29:18.951 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=indexcoord] [serverID=3]
[2025/08/07 15:29:18.951 +08:00] [INFO] [client/client.go:108] ["IndexCoordClient GetSessions success"] [key=indexcoord] [msess="{\"indexcoord\":{\"ServerID\":3,\"ServerName\":\"indexcoord\",\"Address\":\"**************:40003\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}}"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/index_coord.go:142] ["IndexCoord Register Finished"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=6]
[2025/08/07 15:29:18.951 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=indexcoord] [key=indexcoord] [address=**************:40003]
[2025/08/07 15:29:18.951 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:18.951 +08:00] [INFO] [client/client.go:108] ["IndexCoordClient GetSessions success"] [key=indexcoord] [msess="{\"indexcoord\":{\"ServerID\":3,\"ServerName\":\"indexcoord\",\"Address\":\"**************:40003\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}}"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/service.go:199] ["IndexCoord registers service successfully"]
[2025/08/07 15:29:18.951 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:18.951 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" "]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/index_coord.go:1190] ["IndexCoord watchNodeLoop SessionAddEvent"] [serverID=1] [address=**************:40004]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/index_builder.go:141] ["index builder schedule loop start"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/node_manager.go:95] ["IndexCoord addNode"] [nodeID=1] ["node address"=**************:40004]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/index_coord.go:1333] ["IndexCoord start watching flushed segments..."]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/garbage_collector.go:196] ["IndexCoord garbageCollector recycleUnusedSegIndexes start"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/garbage_collector.go:81] ["IndexCoord garbageCollector recycleUnusedIndexes start"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/flush_segment_watcher.go:145] ["IndexCoord flushedSegmentWatcher internalScheduler start..."]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/garbage_collector.go:215] ["IndexCoord garbageCollector start recycleUnusedIndexFiles loop"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="datanode-6\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="indexcoord-3\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [datanode/data_node.go:240] ["DataNode server init rateCollector done"] ["node ID"=0]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/index_coord.go:290] ["IndexCoord start successfully"] [state=Healthy]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/service.go:204] ["indexCoord started"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [indexcoord/service.go:92] ["IndexCoord start done ..."]
[2025/08/07 15:29:18.951 +08:00] [DEBUG] [components/index_coord.go:55] ["IndexCoord successfully started"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [datanode/data_node.go:252] ["DataNode server init succeeded"] [MsgChannelSubName=by-dev-dataNode]
[2025/08/07 15:29:18.951 +08:00] [INFO] [datanode/service.go:321] ["current DataNode state"] [state=Initializing]
[2025/08/07 15:29:18.951 +08:00] [INFO] [datanode/service.go:195] ["DataNode gRPC services successfully initialized"]
[2025/08/07 15:29:18.951 +08:00] [INFO] [datanode/data_node.go:480] ["start id allocator done"] [role=datanode]
[2025/08/07 15:29:18.952 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=datanode] [ServerID=6]
[2025/08/07 15:29:18.952 +08:00] [INFO] [datanode/data_node.go:462] ["DataNode Background GC Start"]
[2025/08/07 15:29:18.953 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/datanode-6] [value="{\"ServerID\":6,\"ServerName\":\"datanode\",\"Address\":\"**************:40006\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:18.953 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=datanode] [serverID=6]
[2025/08/07 15:29:18.953 +08:00] [INFO] [sessionutil/session_util.go:690] ["watch services"] ["add kv"="key:\"by-dev/meta/session/datanode-6\" create_revision:25 mod_revision:25 version:1 value:\"{\\\"ServerID\\\":6,\\\"ServerName\\\":\\\"datanode\\\",\\\"Address\\\":\\\"**************:40006\\\",\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637461779330 "]
[2025/08/07 15:29:18.953 +08:00] [INFO] [datanode/data_node.go:183] ["DataNode Register Finished"]
[2025/08/07 15:29:18.953 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionAddEvent]
[2025/08/07 15:29:18.953 +08:00] [INFO] [datacoord/server.go:784] ["received datanode register"] [address=**************:40006] [serverID=6]
[2025/08/07 15:29:18.953 +08:00] [INFO] [datacoord/channel_manager.go:341] ["register node with no reassignment"] ["registered node"=6]
[2025/08/07 15:29:18.953 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:18.953 +08:00] [INFO] [datanode/service.go:200] ["DataNode gRPC services successfully started"]
[2025/08/07 15:29:18.953 +08:00] [DEBUG] [components/data_node.go:56] ["Datanode successfully started"]
[2025/08/07 15:29:18.955 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=indexnode] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=indexnode] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=indexnode] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=indexnode] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=indexnode] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=indexnode] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=indexnode] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=indexnode] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:18.956 +08:00] [DEBUG] [indexcoord/node_manager.go:59] ["IndexCoord NodeManager setClient"] [nodeID=1]
[2025/08/07 15:29:18.956 +08:00] [INFO] [indexcoord/node_manager.go:69] ["IndexNode NodeManager setClient success"] [nodeID=1] ["IndexNode num"=1]
[2025/08/07 15:29:18.977 +08:00] [WARN] [rootcoord/proxy_client_manager.go:247] ["proxy client is empty, GetMetrics will not send to any client"]
[2025/08/07 15:29:18.977 +08:00] [WARN] [client/client.go:94] ["QueryCoordClient msess key not existed"] [key=querycoord]
[2025/08/07 15:29:18.977 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=datacoord] [key=datacoord] [address=**************:40005]
[2025/08/07 15:29:18.977 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available querycoord, check querycoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:272\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetMetrics\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/client/client.go:361\ngithub.com/milvus-io/milvus/internal/rootcoord.(*QuotaCenter).syncMetrics.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/rootcoord/quota_center.go:188\ngolang.org/x/sync/errgroup.(*Group).Go.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.1.0/errgroup/errgroup.go:75"]
[2025/08/07 15:29:18.977 +08:00] [WARN] [grpcclient/client.go:274] ["fail to get grpc client"] [client_role=querycoord] [error="find no available querycoord, check querycoord state"]
[2025/08/07 15:29:18.977 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=querycoord]
[2025/08/07 15:29:18.977 +08:00] [WARN] [client/client.go:94] ["QueryCoordClient msess key not existed"] [key=querycoord]
[2025/08/07 15:29:18.977 +08:00] [ERROR] [grpcclient/client.go:161] ["failed to get client address"] [error="find no available querycoord, check querycoord state"] [stack="github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:161\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:134\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:279\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:291\ngithub.com/milvus-io/milvus/internal/util/retry.Do\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/retry/retry.go:39\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:287\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:367\ngithub.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/util/grpcclient/client.go:382\ngithub.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetMetrics\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/distributed/querycoord/client/client.go:361\ngithub.com/milvus-io/milvus/internal/rootcoord.(*QuotaCenter).syncMetrics.func1\n\tD:/a/milvus-lite/milvus-lite/milvus_binary/milvus/internal/rootcoord/quota_center.go:188\ngolang.org/x/sync/errgroup.(*Group).Go.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.1.0/errgroup/errgroup.go:75"]
[2025/08/07 15:29:18.977 +08:00] [WARN] [grpcclient/client.go:281] ["fail to get grpc client in the retry state"] [client_role=querycoord] [error="find no available querycoord, check querycoord state"]
[2025/08/07 15:29:18.982 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=datanode] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=datanode] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=datanode] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=datanode] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=datanode] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=datanode] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=datanode] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:18.983 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=datanode] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:19.008 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.008 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.008 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.008 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.008 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.008 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.040 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=datanode] [key=by-dev/meta/session/datanode-6]
[2025/08/07 15:29:19.117 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.117 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.117 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.117 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.117 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.117 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.162 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.162 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.162 +08:00] [INFO] [querycoord/service.go:232] ["QueryCoord report IndexCoord is ready"]
[2025/08/07 15:29:19.162 +08:00] [INFO] [querycoordv2/server.go:176] ["QueryCoord start init"] [meta-root-path=by-dev/meta] [address=**************:40001]
[2025/08/07 15:29:19.162 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:19.162 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:19.164 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=7]
[2025/08/07 15:29:19.164 +08:00] [INFO] [querycoordv2/server.go:209] [QueryCoord] [State=Initializing]
[2025/08/07 15:29:19.164 +08:00] [INFO] [querycoordv2/server.go:214] ["query coordinator try to connect etcd success"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [tso/tso.go:121] ["sync and save timestamp"] [last=0001/01/01 00:00:00.000 +00:00] [save=2025/08/07 15:29:22.165 +08:00] [next=2025/08/07 15:29:19.165 +08:00]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:302] ["init meta"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:312] ["recover meta..."]
[2025/08/07 15:29:19.166 +08:00] [INFO] [meta/collection_manager.go:89] ["recover collections and partitions from kv store"] [traceID=1754551759166069400]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:238] ["init session"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:242] ["init schedulers"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:255] ["init dist controller"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:265] ["init all available balancer"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:274] ["use config balancer"] [balancer=ScoreBasedBalancer]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:281] ["init checker controller"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:345] ["init observers"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoordv2/server.go:297] ["QueryCoord init success"]
[2025/08/07 15:29:19.166 +08:00] [INFO] [querycoord/service.go:105] ["QueryCoord init done ..."]
[2025/08/07 15:29:19.166 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=querycoord] [ServerID=7]
[2025/08/07 15:29:19.167 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/querycoord] [value="{\"ServerID\":7,\"ServerName\":\"querycoord\",\"Address\":\"**************:40001\",\"Exclusive\":true,\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:19.167 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=querycoord] [serverID=7]
[2025/08/07 15:29:19.167 +08:00] [INFO] [querycoordv2/server.go:144] ["QueryCoord Register Finished"]
[2025/08/07 15:29:19.167 +08:00] [INFO] [sessionutil/session_util.go:690] ["watch services"] ["add kv"="key:\"by-dev/meta/session/querycoord\" create_revision:29 mod_revision:29 version:1 value:\"{\\\"ServerID\\\":7,\\\"ServerName\\\":\\\"querycoord\\\",\\\"Address\\\":\\\"**************:40001\\\",\\\"Exclusive\\\":true,\\\"TriggerKill\\\":true,\\\"Version\\\":\\\"2.2.16\\\"}\" lease:7587888637461779350 "]
[2025/08/07 15:29:19.167 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:19.167 +08:00] [INFO] [sessionutil/session_util.go:718] [WatchService] ["event type"=SessionAddEvent]
[2025/08/07 15:29:19.167 +08:00] [INFO] [datacoord/server.go:713] ["there is a new service online"] ["server role"=QueryCoord] ["server ID"=7]
[2025/08/07 15:29:19.167 +08:00] [INFO] [querycoordv2/server.go:388] ["start watcher..."]
[2025/08/07 15:29:19.167 +08:00] [INFO] [sessionutil/session_util.go:561] ["SessionUtil GetSessions "] [prefix=querynode] [key=querynode-2] [address=**************:40002]
[2025/08/07 15:29:19.167 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=querynode] [key=querynode-2] [address=**************:40002]
[2025/08/07 15:29:19.167 +08:00] [INFO] [task/scheduler.go:226] ["add executor for new QueryNode"] [nodeID=2]
[2025/08/07 15:29:19.167 +08:00] [INFO] [dist/dist_handler.go:59] ["start dist handler"] [nodeID=2]
[2025/08/07 15:29:19.168 +08:00] [INFO] [meta/resource_manager.go:468] ["HandleNodeUp: add node to default resource group"] [rgName=__default_resource_group] [node=2]
[2025/08/07 15:29:19.168 +08:00] [INFO] [querycoordv2/server.go:726] ["HandleNodeUp: assign node to resource group"] [nodeID=2] [resourceGroup=__default_resource_group]
[2025/08/07 15:29:19.168 +08:00] [INFO] [querycoordv2/server.go:410] ["start recovering dist and target"]
[2025/08/07 15:29:19.171 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=querynode] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=querynode] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=querynode] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=querynode] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=querynode] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=querynode] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=querynode] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:19.172 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=querynode] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:19.173 +08:00] [INFO] [querycoordv2/server.go:423] ["start cluster..."]
[2025/08/07 15:29:19.173 +08:00] [INFO] [querycoordv2/server.go:426] ["start job scheduler..."]
[2025/08/07 15:29:19.173 +08:00] [INFO] [querycoordv2/server.go:429] ["start task scheduler..."]
[2025/08/07 15:29:19.173 +08:00] [INFO] [querycoordv2/server.go:432] ["start checker controller..."]
[2025/08/07 15:29:19.173 +08:00] [INFO] [querycoordv2/server.go:435] ["start observers..."]
[2025/08/07 15:29:19.173 +08:00] [INFO] [observers/target_observer.go:96] ["Start update next target loop"]
[2025/08/07 15:29:19.173 +08:00] [INFO] [observers/replica_observer.go:64] ["Start check replica loop"]
[2025/08/07 15:29:19.173 +08:00] [INFO] [observers/resource_observer.go:61] ["Start check resource group loop"]
[2025/08/07 15:29:19.174 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="querycoord-7\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:19.174 +08:00] [INFO] [querycoordv2/server.go:382] ["QueryCoord started"]
[2025/08/07 15:29:19.174 +08:00] [INFO] [querycoord/service.go:110] ["QueryCoord start done ..."]
[2025/08/07 15:29:19.174 +08:00] [DEBUG] [components/query_coord.go:56] ["QueryCoord successfully started"]
[2025/08/07 15:29:19.226 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.226 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.226 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.226 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.226 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.226 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.258 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=querycoord] [key=by-dev/meta/session/querycoord]
[2025/08/07 15:29:19.336 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.336 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.337 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.337 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.337 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.337 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.445 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.445 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.445 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.445 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.445 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.445 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.554 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.554 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.554 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.554 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.554 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.554 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.665 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.665 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.665 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.665 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.665 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.665 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.772 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.772 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.772 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.772 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.772 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.772 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.883 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.883 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.883 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.883 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.883 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.883 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:19.962 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.962 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.962 +08:00] [INFO] [proxy/service.go:601] ["Proxy wait for IndexCoord to be healthy done"]
[2025/08/07 15:29:19.962 +08:00] [INFO] [proxy/service.go:604] ["set IndexCoord client for Proxy done"]
[2025/08/07 15:29:19.962 +08:00] [INFO] [proxy/service.go:608] ["create QueryCoord client for Proxy"]
[2025/08/07 15:29:19.962 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:19.962 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:19.962 +08:00] [INFO] [proxy/service.go:614] ["create QueryCoord client for Proxy done"]
[2025/08/07 15:29:19.962 +08:00] [INFO] [proxy/service.go:617] ["init QueryCoord client for Proxy"]
[2025/08/07 15:29:19.963 +08:00] [INFO] [proxy/service.go:622] ["init QueryCoord client for Proxy done"]
[2025/08/07 15:29:19.963 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=querycoord] [key=querycoord] [address=**************:40001]
[2025/08/07 15:29:19.964 +08:00] [INFO] [proxy/service.go:628] ["Proxy wait for QueryCoord to be healthy done"]
[2025/08/07 15:29:19.964 +08:00] [INFO] [proxy/service.go:631] ["set QueryCoord client for Proxy done"]
[2025/08/07 15:29:19.964 +08:00] [INFO] [proxy/service.go:633] ["update Proxy's state to Initializing"]
[2025/08/07 15:29:19.964 +08:00] [INFO] [proxy/service.go:636] ["init Proxy"]
[2025/08/07 15:29:19.964 +08:00] [INFO] [proxy/proxy.go:184] ["init session for Proxy"]
[2025/08/07 15:29:19.964 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:29:19.964 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:29:19.966 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=8]
[2025/08/07 15:29:19.966 +08:00] [INFO] [sessionutil/session_util.go:1055] ["save server info into file"] [content="proxy-8\n"] [filePath="\\tmp\\milvus\\server_id_14580"]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:189] ["init session for Proxy done"]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:192] ["init parameters for factory"] [role=proxy] [parametersError="json: unsupported type: func(log.Config)"]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:198] ["Proxy init rateCollector done"] [nodeID=8]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:200] ["create id allocator"] [role=proxy] [ProxyID=8]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:209] ["create id allocator done"] [role=proxy] [ProxyID=8]
[2025/08/07 15:29:19.966 +08:00] [DEBUG] [proxy/proxy.go:211] ["create timestamp allocator"] [role=proxy] [ProxyID=8]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:220] ["create timestamp allocator done"] [role=proxy] [ProxyID=8]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:222] ["create segment id assigner"] [role=proxy] [ProxyID=8]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:232] ["create segment id assigner done"] [role=proxy] [ProxyID=8]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:234] ["create channels manager"] [role=proxy]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:238] ["create channels manager done"] [role=proxy]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:240] ["create task scheduler"] [role=proxy]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:246] ["create task scheduler done"] [role=proxy]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:249] ["create channels time ticker"] [role=proxy] [syncTimeTickInterval=100ms]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:252] ["create channels time ticker done"] [role=proxy]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:254] ["create metrics cache manager"] [role=proxy]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:256] ["create metrics cache manager done"] [role=proxy]
[2025/08/07 15:29:19.966 +08:00] [INFO] [proxy/proxy.go:258] ["init meta cache"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/meta_cache.go:200] ["success to init meta cache"] [policy_infos="[\"{\\\"PType\\\":\\\"p\\\",\\\"V0\\\":\\\"public\\\",\\\"V1\\\":\\\"Collection-*.*\\\",\\\"V2\\\":\\\"PrivilegeIndexDetail\\\"}\",\"{\\\"PType\\\":\\\"p\\\",\\\"V0\\\":\\\"public\\\",\\\"V1\\\":\\\"Global-*.*\\\",\\\"V2\\\":\\\"PrivilegeDescribeCollection\\\"}\",\"{\\\"PType\\\":\\\"p\\\",\\\"V0\\\":\\\"public\\\",\\\"V1\\\":\\\"Global-*.*\\\",\\\"V2\\\":\\\"PrivilegeShowCollections\\\"}\"]"]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:263] ["init meta cache done"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/meta_cache.go:840] ["updating shard leader cache every"] [interval=3s]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/service.go:641] ["init Proxy done"]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/service.go:374] ["start Proxy server"]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:352] ["start task scheduler"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:357] ["start task scheduler done"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:359] ["start id allocator"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:364] ["start id allocator done"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:366] ["start segment id assigner"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:371] ["start segment id assigner done"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:373] ["start channels time ticker"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:378] ["start channels time ticker done"] [role=proxy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [proxy/proxy.go:391] ["update state code"] [role=proxy] [State=Healthy]
[2025/08/07 15:29:19.967 +08:00] [INFO] [sessionutil/session_util.go:368] ["service begin to register to etcd"] [serverName=proxy] [ServerID=8]
[2025/08/07 15:29:19.969 +08:00] [INFO] [sessionutil/session_util.go:405] ["put session key into etcd"] [key=by-dev/meta/session/proxy-8] [value="{\"ServerID\":8,\"ServerName\":\"proxy\",\"Address\":\"**************:19529\",\"TriggerKill\":true,\"Version\":\"2.2.16\"}"]
[2025/08/07 15:29:19.969 +08:00] [INFO] [sessionutil/session_util.go:417] ["Service registered successfully"] [ServerName=proxy] [serverID=8]
[2025/08/07 15:29:19.969 +08:00] [INFO] [rootcoord/proxy_manager.go:150] ["received proxy put event with session"] [session="Session:<ServerID: 8, ServerName: proxy, Version: 2.2.16>"]
[2025/08/07 15:29:19.969 +08:00] [INFO] [proxy/proxy.go:135] ["Proxy Register Finished"]
[2025/08/07 15:29:19.969 +08:00] [INFO] [rootcoord/timeticksync.go:226] ["Add session for timeticksync"] [serverID=8]
[2025/08/07 15:29:19.969 +08:00] [WARN] [sessionutil/session_util.go:337] ["fail to get the session key from the etcd"] []
[2025/08/07 15:29:19.969 +08:00] [INFO] [proxy/service.go:660] ["start Proxy http server"]
[2025/08/07 15:29:19.969 +08:00] [INFO] [proxy/service.go:379] ["start Proxy server done"]
[2025/08/07 15:29:19.969 +08:00] [INFO] [components/proxy.go:55] ["Proxy successfully started"]
[2025/08/07 15:29:19.973 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=proxy] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=proxy] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=proxy] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=proxy] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=proxy] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=proxy] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=proxy] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:29:19.974 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=proxy] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:29:19.974 +08:00] [INFO] [rootcoord/proxy_client_manager.go:110] ["succeed to create proxy client"] [address=**************:19529] [serverID=8]
[2025/08/07 15:29:19.991 +08:00] [WARN] [grpcclient/client.go:290] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=querycoord]
[2025/08/07 15:29:19.991 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=querycoord] [key=querycoord] [address=**************:40001]
[2025/08/07 15:29:19.992 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:29:19.992 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:3 role:\"IndexCoord\" state_code:Healthy "]
[2025/08/07 15:29:19.992 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Healthy]
[2025/08/07 15:29:19.992 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Healthy]
[2025/08/07 15:29:19.992 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:29:19.992 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:1 role:\"indexnode\" state_code:Healthy "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:29:20.023 +08:00] [INFO] [sessionutil/session_util.go:814] ["register session success"] [role=proxy] [key=by-dev/meta/session/proxy-8]
[2025/08/07 15:29:21.018 +08:00] [INFO] [proxy/impl.go:5577] ["connect received"] [traceID=1c5bd5d158a14910] [sdk_type=Python] [sdk_version=2.6.0] [local_time="2025-08-07 15:29:21.016837"] [user=] [host=WIN-20240508NVR] [db=default]
[2025/08/07 15:29:21.018 +08:00] [INFO] [rootcoord/root_coord.go:908] ["received request to list databases"] [traceID=1c5bd5d158a14910] [msgID=0]
[2025/08/07 15:29:21.018 +08:00] [INFO] [rootcoord/root_coord.go:936] ["done to list databases"] [traceID=1c5bd5d158a14910] [msgID=0] ["num of databases"=1]
[2025/08/07 15:29:21.019 +08:00] [INFO] [proxy/client_info.go:45] ["client register"] [traceID=1c5bd5d158a14910] [sdk_type=Python] [sdk_version=2.6.0] [local_time="2025-08-07 15:29:21.016837"] [user=] [host=WIN-20240508NVR] [identifier=459945216839516162] [last_active_time=2025/08/07 15:29:21.019 +08:00] [db=default]

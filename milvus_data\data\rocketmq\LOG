2025/08/07-15:31:41.216413 6f64 RocksDB version: 6.26.1
2025/08/07-15:31:41.216445 6f64 Git sha 0
2025/08/07-15:31:41.216455 6f64 Compile date 2021-12-13 18:23:52
2025/08/07-15:31:41.216461 6f64 DB SUMMARY
2025/08/07-15:31:41.216466 6f64 DB Session ID:  AS05M3QP3EDL178NAAEM
2025/08/07-15:31:41.216533 6f64 CURRENT file:  CURRENT
2025/08/07-15:31:41.216539 6f64 IDENTITY file:  IDENTITY
2025/08/07-15:31:41.216550 6f64 MANIFEST file:  MANIFEST-000004 size: 57 Bytes
2025/08/07-15:31:41.216556 6f64 SST files in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq dir, Total Num: 0, files: 
2025/08/07-15:31:41.216562 6f64 Write Ahead Log file in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq: 000005.log size: 0 ; 
2025/08/07-15:31:41.216568 6f64                         Options.error_if_exists: 0
2025/08/07-15:31:41.216646 6f64                       Options.create_if_missing: 1
2025/08/07-15:31:41.216652 6f64                         Options.paranoid_checks: 1
2025/08/07-15:31:41.216654 6f64             Options.flush_verify_memtable_count: 1
2025/08/07-15:31:41.216656 6f64                               Options.track_and_verify_wals_in_manifest: 0
2025/08/07-15:31:41.216658 6f64                                     Options.env: 0000022cd13db880
2025/08/07-15:31:41.216661 6f64                                      Options.fs: WinFS
2025/08/07-15:31:41.216663 6f64                                Options.info_log: 0000022cd1467980
2025/08/07-15:31:41.216665 6f64                Options.max_file_opening_threads: 16
2025/08/07-15:31:41.216667 6f64                              Options.statistics: 0000000000000000
2025/08/07-15:31:41.216669 6f64                               Options.use_fsync: 0
2025/08/07-15:31:41.216671 6f64                       Options.max_log_file_size: 0
2025/08/07-15:31:41.216674 6f64                  Options.max_manifest_file_size: 1073741824
2025/08/07-15:31:41.216676 6f64                   Options.log_file_time_to_roll: 0
2025/08/07-15:31:41.216678 6f64                       Options.keep_log_file_num: 1000
2025/08/07-15:31:41.216680 6f64                    Options.recycle_log_file_num: 0
2025/08/07-15:31:41.216682 6f64                         Options.allow_fallocate: 1
2025/08/07-15:31:41.216684 6f64                        Options.allow_mmap_reads: 0
2025/08/07-15:31:41.216686 6f64                       Options.allow_mmap_writes: 0
2025/08/07-15:31:41.216688 6f64                        Options.use_direct_reads: 0
2025/08/07-15:31:41.216690 6f64                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/07-15:31:41.216692 6f64          Options.create_missing_column_families: 0
2025/08/07-15:31:41.216694 6f64                              Options.db_log_dir: 
2025/08/07-15:31:41.216697 6f64                                 Options.wal_dir: 
2025/08/07-15:31:41.216699 6f64                Options.table_cache_numshardbits: 6
2025/08/07-15:31:41.216701 6f64                         Options.WAL_ttl_seconds: 0
2025/08/07-15:31:41.216703 6f64                       Options.WAL_size_limit_MB: 0
2025/08/07-15:31:41.216705 6f64                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/07-15:31:41.216707 6f64             Options.manifest_preallocation_size: 4194304
2025/08/07-15:31:41.216709 6f64                     Options.is_fd_close_on_exec: 1
2025/08/07-15:31:41.216711 6f64                   Options.advise_random_on_open: 1
2025/08/07-15:31:41.216713 6f64                   Options.experimental_mempurge_threshold: 0.000000
2025/08/07-15:31:41.216716 6f64                    Options.db_write_buffer_size: 0
2025/08/07-15:31:41.216719 6f64                    Options.write_buffer_manager: 0000022cd144d630
2025/08/07-15:31:41.216721 6f64         Options.access_hint_on_compaction_start: 1
2025/08/07-15:31:41.216723 6f64  Options.new_table_reader_for_compaction_inputs: 0
2025/08/07-15:31:41.216725 6f64           Options.random_access_max_buffer_size: 1048576
2025/08/07-15:31:41.216727 6f64                      Options.use_adaptive_mutex: 0
2025/08/07-15:31:41.216729 6f64                            Options.rate_limiter: 0000000000000000
2025/08/07-15:31:41.216747 6f64     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/07-15:31:41.216750 6f64                       Options.wal_recovery_mode: 2
2025/08/07-15:31:41.216752 6f64                  Options.enable_thread_tracking: 0
2025/08/07-15:31:41.216754 6f64                  Options.enable_pipelined_write: 0
2025/08/07-15:31:41.216756 6f64                  Options.unordered_write: 0
2025/08/07-15:31:41.216758 6f64         Options.allow_concurrent_memtable_write: 1
2025/08/07-15:31:41.216760 6f64      Options.enable_write_thread_adaptive_yield: 1
2025/08/07-15:31:41.216762 6f64             Options.write_thread_max_yield_usec: 100
2025/08/07-15:31:41.216764 6f64            Options.write_thread_slow_yield_usec: 3
2025/08/07-15:31:41.216766 6f64                               Options.row_cache: None
2025/08/07-15:31:41.216769 6f64                              Options.wal_filter: None
2025/08/07-15:31:41.216771 6f64             Options.avoid_flush_during_recovery: 0
2025/08/07-15:31:41.216773 6f64             Options.allow_ingest_behind: 0
2025/08/07-15:31:41.216775 6f64             Options.preserve_deletes: 0
2025/08/07-15:31:41.216777 6f64             Options.two_write_queues: 0
2025/08/07-15:31:41.216779 6f64             Options.manual_wal_flush: 0
2025/08/07-15:31:41.216781 6f64             Options.atomic_flush: 0
2025/08/07-15:31:41.216783 6f64             Options.avoid_unnecessary_blocking_io: 0
2025/08/07-15:31:41.216785 6f64                 Options.persist_stats_to_disk: 0
2025/08/07-15:31:41.216787 6f64                 Options.write_dbid_to_manifest: 0
2025/08/07-15:31:41.216789 6f64                 Options.log_readahead_size: 0
2025/08/07-15:31:41.216791 6f64                 Options.file_checksum_gen_factory: Unknown
2025/08/07-15:31:41.216793 6f64                 Options.best_efforts_recovery: 0
2025/08/07-15:31:41.216795 6f64                Options.max_bgerror_resume_count: 2147483647
2025/08/07-15:31:41.216797 6f64            Options.bgerror_resume_retry_interval: 1000000
2025/08/07-15:31:41.216799 6f64             Options.allow_data_in_errors: 0
2025/08/07-15:31:41.216801 6f64             Options.db_host_id: __hostname__
2025/08/07-15:31:41.216804 6f64             Options.max_background_jobs: 2
2025/08/07-15:31:41.216806 6f64             Options.max_background_compactions: -1
2025/08/07-15:31:41.216808 6f64             Options.max_subcompactions: 1
2025/08/07-15:31:41.216810 6f64             Options.avoid_flush_during_shutdown: 0
2025/08/07-15:31:41.216812 6f64           Options.writable_file_max_buffer_size: 1048576
2025/08/07-15:31:41.216814 6f64             Options.delayed_write_rate : 16777216
2025/08/07-15:31:41.216816 6f64             Options.max_total_wal_size: 0
2025/08/07-15:31:41.216818 6f64             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/07-15:31:41.216820 6f64                   Options.stats_dump_period_sec: 600
2025/08/07-15:31:41.216822 6f64                 Options.stats_persist_period_sec: 600
2025/08/07-15:31:41.216824 6f64                 Options.stats_history_buffer_size: 1048576
2025/08/07-15:31:41.216826 6f64                          Options.max_open_files: -1
2025/08/07-15:31:41.216828 6f64                          Options.bytes_per_sync: 0
2025/08/07-15:31:41.216831 6f64                      Options.wal_bytes_per_sync: 0
2025/08/07-15:31:41.216833 6f64                   Options.strict_bytes_per_sync: 0
2025/08/07-15:31:41.216835 6f64       Options.compaction_readahead_size: 0
2025/08/07-15:31:41.216837 6f64                  Options.max_background_flushes: 1
2025/08/07-15:31:41.216839 6f64 Compression algorithms supported:
2025/08/07-15:31:41.216841 6f64 	kZSTD supported: 1
2025/08/07-15:31:41.216843 6f64 	kXpressCompression supported: 0
2025/08/07-15:31:41.216845 6f64 	kBZip2Compression supported: 1
2025/08/07-15:31:41.216847 6f64 	kZSTDNotFinalCompression supported: 1
2025/08/07-15:31:41.216849 6f64 	kLZ4Compression supported: 1
2025/08/07-15:31:41.216851 6f64 	kZlibCompression supported: 1
2025/08/07-15:31:41.216854 6f64 	kLZ4HCCompression supported: 1
2025/08/07-15:31:41.216864 6f64 	kSnappyCompression supported: 1
2025/08/07-15:31:41.216869 6f64 Fast CRC32 supported: Supported on x86
2025/08/07-15:31:41.217166 6f64 [db/version_set.cc:4847] Recovering from manifest file: D:\AAAAA-wq-work\access\milvus_data\data\rocketmq/MANIFEST-000004
2025/08/07-15:31:41.217224 6f64 [db/column_family.cc:609] --------------- Options for column family [default]:
2025/08/07-15:31:41.217227 6f64               Options.comparator: leveldb.BytewiseComparator
2025/08/07-15:31:41.217230 6f64           Options.merge_operator: None
2025/08/07-15:31:41.217232 6f64        Options.compaction_filter: None
2025/08/07-15:31:41.217234 6f64        Options.compaction_filter_factory: None
2025/08/07-15:31:41.217236 6f64  Options.sst_partitioner_factory: None
2025/08/07-15:31:41.217238 6f64         Options.memtable_factory: SkipListFactory
2025/08/07-15:31:41.217240 6f64            Options.table_factory: BlockBasedTable
2025/08/07-15:31:41.217254 6f64            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000022cd144f470)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0000022cd13f2b60
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 2566541721
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/07-15:31:41.217258 6f64        Options.write_buffer_size: 67108864
2025/08/07-15:31:41.217260 6f64  Options.max_write_buffer_number: 2
2025/08/07-15:31:41.217262 6f64        Options.compression[0]: NoCompression
2025/08/07-15:31:41.217264 6f64        Options.compression[1]: NoCompression
2025/08/07-15:31:41.217267 6f64        Options.compression[2]: ZSTD
2025/08/07-15:31:41.217269 6f64        Options.compression[3]: ZSTD
2025/08/07-15:31:41.217271 6f64        Options.compression[4]: ZSTD
2025/08/07-15:31:41.217273 6f64        Options.compression[5]: ZSTD
2025/08/07-15:31:41.217275 6f64        Options.compression[6]: ZSTD
2025/08/07-15:31:41.217277 6f64                  Options.bottommost_compression: Disabled
2025/08/07-15:31:41.217279 6f64       Options.prefix_extractor: nullptr
2025/08/07-15:31:41.217281 6f64   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/07-15:31:41.217283 6f64             Options.num_levels: 7
2025/08/07-15:31:41.217285 6f64        Options.min_write_buffer_number_to_merge: 1
2025/08/07-15:31:41.217287 6f64     Options.max_write_buffer_number_to_maintain: 0
2025/08/07-15:31:41.217289 6f64     Options.max_write_buffer_size_to_maintain: 0
2025/08/07-15:31:41.217291 6f64            Options.bottommost_compression_opts.window_bits: -14
2025/08/07-15:31:41.217293 6f64                  Options.bottommost_compression_opts.level: 32767
2025/08/07-15:31:41.217295 6f64               Options.bottommost_compression_opts.strategy: 0
2025/08/07-15:31:41.217297 6f64         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/07-15:31:41.217300 6f64         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:31:41.217302 6f64         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/07-15:31:41.217304 6f64                  Options.bottommost_compression_opts.enabled: false
2025/08/07-15:31:41.217307 6f64         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:31:41.217309 6f64            Options.compression_opts.window_bits: -14
2025/08/07-15:31:41.217311 6f64                  Options.compression_opts.level: 32767
2025/08/07-15:31:41.217313 6f64               Options.compression_opts.strategy: 0
2025/08/07-15:31:41.217316 6f64         Options.compression_opts.max_dict_bytes: 0
2025/08/07-15:31:41.217318 6f64         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:31:41.217320 6f64         Options.compression_opts.parallel_threads: 1
2025/08/07-15:31:41.217322 6f64                  Options.compression_opts.enabled: false
2025/08/07-15:31:41.217324 6f64         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:31:41.217326 6f64      Options.level0_file_num_compaction_trigger: 4
2025/08/07-15:31:41.217328 6f64          Options.level0_slowdown_writes_trigger: 20
2025/08/07-15:31:41.217330 6f64              Options.level0_stop_writes_trigger: 36
2025/08/07-15:31:41.217332 6f64                   Options.target_file_size_base: 67108864
2025/08/07-15:31:41.217334 6f64             Options.target_file_size_multiplier: 1
2025/08/07-15:31:41.217336 6f64                Options.max_bytes_for_level_base: 268435456
2025/08/07-15:31:41.217338 6f64 Options.level_compaction_dynamic_level_bytes: 0
2025/08/07-15:31:41.217340 6f64          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/07-15:31:41.217343 6f64 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/07-15:31:41.217345 6f64 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/07-15:31:41.217347 6f64 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/07-15:31:41.217349 6f64 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/07-15:31:41.217351 6f64 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/07-15:31:41.217353 6f64 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/07-15:31:41.217355 6f64 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/07-15:31:41.217357 6f64       Options.max_sequential_skip_in_iterations: 8
2025/08/07-15:31:41.217359 6f64                    Options.max_compaction_bytes: 1677721600
2025/08/07-15:31:41.217361 6f64                        Options.arena_block_size: 1048576
2025/08/07-15:31:41.217363 6f64   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/07-15:31:41.217365 6f64   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/07-15:31:41.217368 6f64       Options.rate_limit_delay_max_milliseconds: 100
2025/08/07-15:31:41.217370 6f64                Options.disable_auto_compactions: 0
2025/08/07-15:31:41.217372 6f64                        Options.compaction_style: kCompactionStyleLevel
2025/08/07-15:31:41.217374 6f64                          Options.compaction_pri: kMinOverlappingRatio
2025/08/07-15:31:41.217376 6f64 Options.compaction_options_universal.size_ratio: 1
2025/08/07-15:31:41.217378 6f64 Options.compaction_options_universal.min_merge_width: 2
2025/08/07-15:31:41.217380 6f64 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/07-15:31:41.217382 6f64 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/07-15:31:41.217385 6f64 Options.compaction_options_universal.compression_size_percent: -1
2025/08/07-15:31:41.217387 6f64 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/07-15:31:41.217389 6f64 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/07-15:31:41.217391 6f64 Options.compaction_options_fifo.allow_compaction: 0
2025/08/07-15:31:41.217394 6f64                   Options.table_properties_collectors: 
2025/08/07-15:31:41.217396 6f64                   Options.inplace_update_support: 0
2025/08/07-15:31:41.217399 6f64                 Options.inplace_update_num_locks: 10000
2025/08/07-15:31:41.217401 6f64               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/07-15:31:41.217403 6f64               Options.memtable_whole_key_filtering: 0
2025/08/07-15:31:41.217405 6f64   Options.memtable_huge_page_size: 0
2025/08/07-15:31:41.217420 6f64                           Options.bloom_locality: 0
2025/08/07-15:31:41.217423 6f64                    Options.max_successive_merges: 0
2025/08/07-15:31:41.217425 6f64                Options.optimize_filters_for_hits: 0
2025/08/07-15:31:41.217427 6f64                Options.paranoid_file_checks: 0
2025/08/07-15:31:41.217429 6f64                Options.force_consistency_checks: 1
2025/08/07-15:31:41.217431 6f64                Options.report_bg_io_stats: 0
2025/08/07-15:31:41.217433 6f64                               Options.ttl: 2592000
2025/08/07-15:31:41.217435 6f64          Options.periodic_compaction_seconds: 0
2025/08/07-15:31:41.217437 6f64                       Options.enable_blob_files: false
2025/08/07-15:31:41.217439 6f64                           Options.min_blob_size: 0
2025/08/07-15:31:41.217441 6f64                          Options.blob_file_size: 268435456
2025/08/07-15:31:41.217443 6f64                   Options.blob_compression_type: NoCompression
2025/08/07-15:31:41.217445 6f64          Options.enable_blob_garbage_collection: false
2025/08/07-15:31:41.217448 6f64      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/07-15:31:41.217450 6f64 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/07-15:31:41.218700 6f64 [db/version_set.cc:4887] Recovered from manifest file:D:\AAAAA-wq-work\access\milvus_data\data\rocketmq/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 6, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/07-15:31:41.218707 6f64 [db/version_set.cc:4902] Column family [default] (ID 0), log number is 0
2025/08/07-15:31:41.218896 6f64 [db/version_set.cc:4385] Creating manifest 8
2025/08/07-15:31:41.221286 6f64 EVENT_LOG_v1 {"time_micros": 1754551901221284, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/08/07-15:31:41.221293 6f64 [db/db_impl/db_impl_open.cc:876] Recovering log #5 mode 2
2025/08/07-15:31:41.221357 6f64 [db/version_set.cc:4385] Creating manifest 9
2025/08/07-15:31:41.224416 6f64 EVENT_LOG_v1 {"time_micros": 1754551901224414, "job": 1, "event": "recovery_finished"}
2025/08/07-15:31:41.225453 6f64 [file/delete_scheduler.cc:73] Deleted file D:\AAAAA-wq-work\access\milvus_data\data\rocketmq/000005.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/07-15:31:41.227748 6f64 [db/db_impl/db_impl_open.cc:1786] SstFileManager instance 0000022d20a35730
2025/08/07-15:31:41.227801 6f64 DB pointer 0000022cd148cb10
2025/08/07-15:31:44.243362 50e0 [db/db_impl/db_impl.cc:1004] ------- DUMPING STATS -------
2025/08/07-15:31:44.243381 50e0 [db/db_impl/db_impl.cc:1006] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x22cd13f2b60#28564 capacity: 2.39 GB collections: 1 last_copies: 1 last_secs: 2.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

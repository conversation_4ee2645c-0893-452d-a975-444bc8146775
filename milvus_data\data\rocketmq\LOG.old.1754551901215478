2025/08/07-15:29:15.191678 68d8 RocksDB version: 6.26.1
2025/08/07-15:29:15.191713 68d8 Git sha 0
2025/08/07-15:29:15.191722 68d8 Compile date 2021-12-13 18:23:52
2025/08/07-15:29:15.191728 68d8 DB SUMMARY
2025/08/07-15:29:15.191734 68d8 DB Session ID:  IXQ7AXG9LQ9PDKXUU6DH
2025/08/07-15:29:15.191790 68d8 SST files in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq dir, Total Num: 0, files: 
2025/08/07-15:29:15.191796 68d8 Write Ahead Log file in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq: 
2025/08/07-15:29:15.191803 68d8                         Options.error_if_exists: 0
2025/08/07-15:29:15.191809 68d8                       Options.create_if_missing: 1
2025/08/07-15:29:15.191814 68d8                         Options.paranoid_checks: 1
2025/08/07-15:29:15.191886 68d8             Options.flush_verify_memtable_count: 1
2025/08/07-15:29:15.191889 68d8                               Options.track_and_verify_wals_in_manifest: 0
2025/08/07-15:29:15.191892 68d8                                     Options.env: 000002847530b880
2025/08/07-15:29:15.191894 68d8                                      Options.fs: WinFS
2025/08/07-15:29:15.191897 68d8                                Options.info_log: 00000284753969e0
2025/08/07-15:29:15.191899 68d8                Options.max_file_opening_threads: 16
2025/08/07-15:29:15.191901 68d8                              Options.statistics: 0000000000000000
2025/08/07-15:29:15.191903 68d8                               Options.use_fsync: 0
2025/08/07-15:29:15.191906 68d8                       Options.max_log_file_size: 0
2025/08/07-15:29:15.191908 68d8                  Options.max_manifest_file_size: 1073741824
2025/08/07-15:29:15.191910 68d8                   Options.log_file_time_to_roll: 0
2025/08/07-15:29:15.191912 68d8                       Options.keep_log_file_num: 1000
2025/08/07-15:29:15.191914 68d8                    Options.recycle_log_file_num: 0
2025/08/07-15:29:15.191917 68d8                         Options.allow_fallocate: 1
2025/08/07-15:29:15.191919 68d8                        Options.allow_mmap_reads: 0
2025/08/07-15:29:15.191921 68d8                       Options.allow_mmap_writes: 0
2025/08/07-15:29:15.191923 68d8                        Options.use_direct_reads: 0
2025/08/07-15:29:15.191925 68d8                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/07-15:29:15.191927 68d8          Options.create_missing_column_families: 0
2025/08/07-15:29:15.191930 68d8                              Options.db_log_dir: 
2025/08/07-15:29:15.191932 68d8                                 Options.wal_dir: 
2025/08/07-15:29:15.191934 68d8                Options.table_cache_numshardbits: 6
2025/08/07-15:29:15.191936 68d8                         Options.WAL_ttl_seconds: 0
2025/08/07-15:29:15.191938 68d8                       Options.WAL_size_limit_MB: 0
2025/08/07-15:29:15.191940 68d8                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/07-15:29:15.191943 68d8             Options.manifest_preallocation_size: 4194304
2025/08/07-15:29:15.191945 68d8                     Options.is_fd_close_on_exec: 1
2025/08/07-15:29:15.191947 68d8                   Options.advise_random_on_open: 1
2025/08/07-15:29:15.191949 68d8                   Options.experimental_mempurge_threshold: 0.000000
2025/08/07-15:29:15.191953 68d8                    Options.db_write_buffer_size: 0
2025/08/07-15:29:15.191955 68d8                    Options.write_buffer_manager: 000002847537d2d0
2025/08/07-15:29:15.191957 68d8         Options.access_hint_on_compaction_start: 1
2025/08/07-15:29:15.191959 68d8  Options.new_table_reader_for_compaction_inputs: 0
2025/08/07-15:29:15.191961 68d8           Options.random_access_max_buffer_size: 1048576
2025/08/07-15:29:15.191964 68d8                      Options.use_adaptive_mutex: 0
2025/08/07-15:29:15.191966 68d8                            Options.rate_limiter: 0000000000000000
2025/08/07-15:29:15.191969 68d8     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/07-15:29:15.191971 68d8                       Options.wal_recovery_mode: 2
2025/08/07-15:29:15.191973 68d8                  Options.enable_thread_tracking: 0
2025/08/07-15:29:15.191988 68d8                  Options.enable_pipelined_write: 0
2025/08/07-15:29:15.191991 68d8                  Options.unordered_write: 0
2025/08/07-15:29:15.191993 68d8         Options.allow_concurrent_memtable_write: 1
2025/08/07-15:29:15.191995 68d8      Options.enable_write_thread_adaptive_yield: 1
2025/08/07-15:29:15.191998 68d8             Options.write_thread_max_yield_usec: 100
2025/08/07-15:29:15.192000 68d8            Options.write_thread_slow_yield_usec: 3
2025/08/07-15:29:15.192002 68d8                               Options.row_cache: None
2025/08/07-15:29:15.192004 68d8                              Options.wal_filter: None
2025/08/07-15:29:15.192006 68d8             Options.avoid_flush_during_recovery: 0
2025/08/07-15:29:15.192008 68d8             Options.allow_ingest_behind: 0
2025/08/07-15:29:15.192011 68d8             Options.preserve_deletes: 0
2025/08/07-15:29:15.192013 68d8             Options.two_write_queues: 0
2025/08/07-15:29:15.192015 68d8             Options.manual_wal_flush: 0
2025/08/07-15:29:15.192017 68d8             Options.atomic_flush: 0
2025/08/07-15:29:15.192019 68d8             Options.avoid_unnecessary_blocking_io: 0
2025/08/07-15:29:15.192021 68d8                 Options.persist_stats_to_disk: 0
2025/08/07-15:29:15.192023 68d8                 Options.write_dbid_to_manifest: 0
2025/08/07-15:29:15.192026 68d8                 Options.log_readahead_size: 0
2025/08/07-15:29:15.192028 68d8                 Options.file_checksum_gen_factory: Unknown
2025/08/07-15:29:15.192030 68d8                 Options.best_efforts_recovery: 0
2025/08/07-15:29:15.192032 68d8                Options.max_bgerror_resume_count: 2147483647
2025/08/07-15:29:15.192034 68d8            Options.bgerror_resume_retry_interval: 1000000
2025/08/07-15:29:15.192037 68d8             Options.allow_data_in_errors: 0
2025/08/07-15:29:15.192039 68d8             Options.db_host_id: __hostname__
2025/08/07-15:29:15.192041 68d8             Options.max_background_jobs: 2
2025/08/07-15:29:15.192043 68d8             Options.max_background_compactions: -1
2025/08/07-15:29:15.192045 68d8             Options.max_subcompactions: 1
2025/08/07-15:29:15.192048 68d8             Options.avoid_flush_during_shutdown: 0
2025/08/07-15:29:15.192050 68d8           Options.writable_file_max_buffer_size: 1048576
2025/08/07-15:29:15.192052 68d8             Options.delayed_write_rate : 16777216
2025/08/07-15:29:15.192054 68d8             Options.max_total_wal_size: 0
2025/08/07-15:29:15.192056 68d8             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/07-15:29:15.192058 68d8                   Options.stats_dump_period_sec: 600
2025/08/07-15:29:15.192061 68d8                 Options.stats_persist_period_sec: 600
2025/08/07-15:29:15.192063 68d8                 Options.stats_history_buffer_size: 1048576
2025/08/07-15:29:15.192065 68d8                          Options.max_open_files: -1
2025/08/07-15:29:15.192067 68d8                          Options.bytes_per_sync: 0
2025/08/07-15:29:15.192069 68d8                      Options.wal_bytes_per_sync: 0
2025/08/07-15:29:15.192071 68d8                   Options.strict_bytes_per_sync: 0
2025/08/07-15:29:15.192074 68d8       Options.compaction_readahead_size: 0
2025/08/07-15:29:15.192076 68d8                  Options.max_background_flushes: 1
2025/08/07-15:29:15.192078 68d8 Compression algorithms supported:
2025/08/07-15:29:15.192080 68d8 	kZSTD supported: 1
2025/08/07-15:29:15.192083 68d8 	kXpressCompression supported: 0
2025/08/07-15:29:15.192085 68d8 	kBZip2Compression supported: 1
2025/08/07-15:29:15.192087 68d8 	kZSTDNotFinalCompression supported: 1
2025/08/07-15:29:15.192089 68d8 	kLZ4Compression supported: 1
2025/08/07-15:29:15.192091 68d8 	kZlibCompression supported: 1
2025/08/07-15:29:15.192093 68d8 	kLZ4HCCompression supported: 1
2025/08/07-15:29:15.192096 68d8 	kSnappyCompression supported: 1
2025/08/07-15:29:15.192100 68d8 Fast CRC32 supported: Supported on x86
2025/08/07-15:29:15.193740 68d8 [db/db_impl/db_impl_open.cc:300] Creating manifest 1 
2025/08/07-15:29:15.196223 68d8 [db/version_set.cc:4847] Recovering from manifest file: D:\AAAAA-wq-work\access\milvus_data\data\rocketmq/MANIFEST-000001
2025/08/07-15:29:15.196294 68d8 [db/column_family.cc:609] --------------- Options for column family [default]:
2025/08/07-15:29:15.196298 68d8               Options.comparator: leveldb.BytewiseComparator
2025/08/07-15:29:15.196300 68d8           Options.merge_operator: None
2025/08/07-15:29:15.196302 68d8        Options.compaction_filter: None
2025/08/07-15:29:15.196305 68d8        Options.compaction_filter_factory: None
2025/08/07-15:29:15.196307 68d8  Options.sst_partitioner_factory: None
2025/08/07-15:29:15.196309 68d8         Options.memtable_factory: SkipListFactory
2025/08/07-15:29:15.196311 68d8            Options.table_factory: BlockBasedTable
2025/08/07-15:29:15.196327 68d8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002847537f9e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0000028475322360
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 2566541721
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/07-15:29:15.196332 68d8        Options.write_buffer_size: 67108864
2025/08/07-15:29:15.196334 68d8  Options.max_write_buffer_number: 2
2025/08/07-15:29:15.196337 68d8        Options.compression[0]: NoCompression
2025/08/07-15:29:15.196339 68d8        Options.compression[1]: NoCompression
2025/08/07-15:29:15.196341 68d8        Options.compression[2]: ZSTD
2025/08/07-15:29:15.196343 68d8        Options.compression[3]: ZSTD
2025/08/07-15:29:15.196345 68d8        Options.compression[4]: ZSTD
2025/08/07-15:29:15.196347 68d8        Options.compression[5]: ZSTD
2025/08/07-15:29:15.196350 68d8        Options.compression[6]: ZSTD
2025/08/07-15:29:15.196352 68d8                  Options.bottommost_compression: Disabled
2025/08/07-15:29:15.196354 68d8       Options.prefix_extractor: nullptr
2025/08/07-15:29:15.196356 68d8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/07-15:29:15.196358 68d8             Options.num_levels: 7
2025/08/07-15:29:15.196360 68d8        Options.min_write_buffer_number_to_merge: 1
2025/08/07-15:29:15.196363 68d8     Options.max_write_buffer_number_to_maintain: 0
2025/08/07-15:29:15.196365 68d8     Options.max_write_buffer_size_to_maintain: 0
2025/08/07-15:29:15.196367 68d8            Options.bottommost_compression_opts.window_bits: -14
2025/08/07-15:29:15.196369 68d8                  Options.bottommost_compression_opts.level: 32767
2025/08/07-15:29:15.196371 68d8               Options.bottommost_compression_opts.strategy: 0
2025/08/07-15:29:15.196374 68d8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/07-15:29:15.196376 68d8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:29:15.196378 68d8         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/07-15:29:15.196380 68d8                  Options.bottommost_compression_opts.enabled: false
2025/08/07-15:29:15.196382 68d8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:29:15.196384 68d8            Options.compression_opts.window_bits: -14
2025/08/07-15:29:15.196388 68d8                  Options.compression_opts.level: 32767
2025/08/07-15:29:15.196391 68d8               Options.compression_opts.strategy: 0
2025/08/07-15:29:15.196393 68d8         Options.compression_opts.max_dict_bytes: 0
2025/08/07-15:29:15.196395 68d8         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:29:15.196397 68d8         Options.compression_opts.parallel_threads: 1
2025/08/07-15:29:15.196400 68d8                  Options.compression_opts.enabled: false
2025/08/07-15:29:15.196402 68d8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:29:15.196404 68d8      Options.level0_file_num_compaction_trigger: 4
2025/08/07-15:29:15.196406 68d8          Options.level0_slowdown_writes_trigger: 20
2025/08/07-15:29:15.196408 68d8              Options.level0_stop_writes_trigger: 36
2025/08/07-15:29:15.196410 68d8                   Options.target_file_size_base: 67108864
2025/08/07-15:29:15.196413 68d8             Options.target_file_size_multiplier: 1
2025/08/07-15:29:15.196415 68d8                Options.max_bytes_for_level_base: 268435456
2025/08/07-15:29:15.196417 68d8 Options.level_compaction_dynamic_level_bytes: 0
2025/08/07-15:29:15.196419 68d8          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/07-15:29:15.196422 68d8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/07-15:29:15.196424 68d8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/07-15:29:15.196426 68d8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/07-15:29:15.196428 68d8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/07-15:29:15.196430 68d8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/07-15:29:15.196432 68d8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/07-15:29:15.196435 68d8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/07-15:29:15.196437 68d8       Options.max_sequential_skip_in_iterations: 8
2025/08/07-15:29:15.196439 68d8                    Options.max_compaction_bytes: 1677721600
2025/08/07-15:29:15.196441 68d8                        Options.arena_block_size: 1048576
2025/08/07-15:29:15.196443 68d8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/07-15:29:15.196445 68d8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/07-15:29:15.196448 68d8       Options.rate_limit_delay_max_milliseconds: 100
2025/08/07-15:29:15.196450 68d8                Options.disable_auto_compactions: 0
2025/08/07-15:29:15.196452 68d8                        Options.compaction_style: kCompactionStyleLevel
2025/08/07-15:29:15.196455 68d8                          Options.compaction_pri: kMinOverlappingRatio
2025/08/07-15:29:15.196457 68d8 Options.compaction_options_universal.size_ratio: 1
2025/08/07-15:29:15.196459 68d8 Options.compaction_options_universal.min_merge_width: 2
2025/08/07-15:29:15.196461 68d8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/07-15:29:15.196463 68d8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/07-15:29:15.196466 68d8 Options.compaction_options_universal.compression_size_percent: -1
2025/08/07-15:29:15.196468 68d8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/07-15:29:15.196470 68d8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/07-15:29:15.196472 68d8 Options.compaction_options_fifo.allow_compaction: 0
2025/08/07-15:29:15.196476 68d8                   Options.table_properties_collectors: 
2025/08/07-15:29:15.196478 68d8                   Options.inplace_update_support: 0
2025/08/07-15:29:15.196481 68d8                 Options.inplace_update_num_locks: 10000
2025/08/07-15:29:15.196483 68d8               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/07-15:29:15.196485 68d8               Options.memtable_whole_key_filtering: 0
2025/08/07-15:29:15.196487 68d8   Options.memtable_huge_page_size: 0
2025/08/07-15:29:15.196489 68d8                           Options.bloom_locality: 0
2025/08/07-15:29:15.196508 68d8                    Options.max_successive_merges: 0
2025/08/07-15:29:15.196511 68d8                Options.optimize_filters_for_hits: 0
2025/08/07-15:29:15.196513 68d8                Options.paranoid_file_checks: 0
2025/08/07-15:29:15.196515 68d8                Options.force_consistency_checks: 1
2025/08/07-15:29:15.196517 68d8                Options.report_bg_io_stats: 0
2025/08/07-15:29:15.196520 68d8                               Options.ttl: 2592000
2025/08/07-15:29:15.196522 68d8          Options.periodic_compaction_seconds: 0
2025/08/07-15:29:15.196524 68d8                       Options.enable_blob_files: false
2025/08/07-15:29:15.196526 68d8                           Options.min_blob_size: 0
2025/08/07-15:29:15.196528 68d8                          Options.blob_file_size: 268435456
2025/08/07-15:29:15.196531 68d8                   Options.blob_compression_type: NoCompression
2025/08/07-15:29:15.196533 68d8          Options.enable_blob_garbage_collection: false
2025/08/07-15:29:15.196535 68d8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/07-15:29:15.196538 68d8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/07-15:29:15.198133 68d8 [db/version_set.cc:4887] Recovered from manifest file:D:\AAAAA-wq-work\access\milvus_data\data\rocketmq/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/07-15:29:15.198143 68d8 [db/version_set.cc:4902] Column family [default] (ID 0), log number is 0
2025/08/07-15:29:15.198376 68d8 [db/version_set.cc:4385] Creating manifest 4
2025/08/07-15:29:15.204221 68d8 [db/db_impl/db_impl_open.cc:1786] SstFileManager instance 00000284a08cbb10
2025/08/07-15:29:15.204280 68d8 DB pointer 00000284a0831090
2025/08/07-15:29:18.210998 653c [db/db_impl/db_impl.cc:1004] ------- DUMPING STATS -------
2025/08/07-15:29:18.211019 653c [db/db_impl/db_impl.cc:1006] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x28475322360#14580 capacity: 2.39 GB collections: 1 last_copies: 1 last_secs: 3.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的Milvus Lite初始化脚本
包含错误处理和重试机制
"""

import sys
import time
import os
import psutil

def check_system_resources():
    """检查系统资源"""
    print("🔍 检查系统资源...")
    
    # 检查内存
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    print(f"  可用内存: {available_gb:.1f} GB")
    
    if available_gb < 2:
        print("⚠️ 警告: 可用内存不足2GB，可能影响Milvus启动")
        return False
    
    # 检查磁盘空间
    disk = psutil.disk_usage('.')
    free_gb = disk.free / (1024**3)
    print(f"  可用磁盘空间: {free_gb:.1f} GB")
    
    if free_gb < 1:
        print("⚠️ 警告: 可用磁盘空间不足1GB")
        return False
    
    return True

def check_port_availability(port=19530):
    """检查端口是否可用"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def safe_start_milvus():
    """安全启动Milvus Lite"""
    try:
        from milvus import default_server
        
        print("🚀 正在启动Milvus Lite...")
        
        # 设置数据目录
        data_dir = "./milvus_lite_data"
        os.makedirs(data_dir, exist_ok=True)
        os.environ['MILVUS_LITE_DATA_PATH'] = data_dir
        
        # 检查端口
        if not check_port_availability():
            print("⚠️ 默认端口19530被占用，Milvus将使用其他端口")
        
        # 启动服务，增加超时时间
        print("⏳ 启动中，请耐心等待（最多60秒）...")
        default_server.start(timeout=60)
        
        print(f"✅ Milvus Lite启动成功!")
        print(f"   监听端口: {default_server.listen_port}")
        print(f"   数据目录: {data_dir}")
        
        return default_server
        
    except Exception as e:
        print(f"❌ Milvus Lite启动失败: {e}")
        
        # 提供详细的错误诊断
        if "TimeoutError" in str(e):
            print("\n🔧 超时错误解决方案:")
            print("1. 系统资源不足，请关闭其他应用程序")
            print("2. 防火墙或安全软件阻止，请检查设置")
            print("3. 端口被占用，请检查其他Milvus实例")
            print("4. 重启计算机后重试")
        
        return None

def test_milvus_connection(server):
    """测试Milvus连接"""
    try:
        from pymilvus import connections, utility
        
        print("🔗 测试Milvus连接...")
        
        # 连接到Milvus
        connections.connect(
            "default",
            host="127.0.0.1",
            port=str(server.listen_port)
        )
        
        # 测试基本操作
        collections = utility.list_collections()
        print(f"✅ 连接成功，当前集合数: {len(collections)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Milvus Lite 安全初始化脚本")
    print("=" * 50)
    
    try:
        # 检查依赖
        try:
            import pymilvus
            import milvus
            import numpy as np
            print("✅ 依赖检查通过")
        except ImportError as e:
            print(f"❌ 缺少依赖: {e}")
            print("请安装: pip install pymilvus milvus numpy")
            return
        
        # 检查系统资源
        if not check_system_resources():
            response = input("系统资源可能不足，是否继续? (y/N): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                print("❌ 用户取消操作")
                return
        
        # 启动Milvus Lite
        server = safe_start_milvus()
        if not server:
            print("❌ 无法启动Milvus Lite")
            return
        
        # 测试连接
        if not test_milvus_connection(server):
            print("❌ Milvus连接测试失败")
            return
        
        # 初始化向量服务
        print("\n📦 初始化向量服务...")
        try:
            from app.vector_service import get_vector_service
            
            vector_service = get_vector_service()
            if vector_service.milvus_available:
                print("✅ 向量服务初始化成功")
                
                # 询问是否构建索引
                response = input("是否构建向量索引? (y/N): ").strip().lower()
                if response in ['y', 'yes', '是']:
                    print("🔄 构建向量索引...")
                    start_time = time.time()
                    vector_service.rebuild_all_indexes()
                    elapsed = time.time() - start_time
                    print(f"✅ 索引构建完成，耗时: {elapsed:.1f}秒")
                    
                    # 测试检索
                    print("🧪 测试向量检索...")
                    results = vector_service.search_similar("维生素", top_k=2, similarity_threshold=0.3)
                    print(f"   找到 {len(results)} 个相似结果")
                    
            else:
                print("❌ 向量服务初始化失败")
                
        except Exception as e:
            print(f"❌ 向量服务初始化异常: {e}")
        
        # 保持服务运行
        print(f"\n🎉 初始化完成!")
        print(f"💡 Milvus Lite正在运行，端口: {server.listen_port}")
        print("   现在可以启动API服务: uvicorn app.main:app --reload")
        
        keep_running = input("\n是否保持Milvus服务运行? (Y/n): ").strip().lower()
        if keep_running not in ['n', 'no', '否']:
            print("🔄 服务运行中，按Ctrl+C停止...")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号")
        
        # 停止服务
        print("🧹 正在停止Milvus服务...")
        server.stop()
        print("✅ 服务已停止")
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 初始化过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # 数据库配置
    database_url: str = "mysql+pymysql://root:root@localhost:3306/food_dataset"
    
    # LLM配置 - 本地部署
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "123456")  # 本地模型通常不需要真实key
    openai_base_url: str = os.getenv("OPENAI_BASE_URL", "http://**************:18005/v1")
    llm_model: str = os.getenv("LLM_MODEL", "Qwen3-32B")
    
    class Config:
        env_file = ".env"

settings = Settings()


[2025/08/07 15:31:41.182 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.188 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.189 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.189 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.189 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.189 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.189 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.189 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.194 +08:00] [INFO] [roles/roles.go:287] ["starting running Milvus components"]
[2025/08/07 15:31:41.196 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.196 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.196 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.196 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.196 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.196 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.197 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.197 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.199 +08:00] [INFO] [server/global_rmq.go:55] ["initializing global rmq"] [path="D:\\AAAAA-wq-work\\access\\milvus_data\\data\\rocketmq"]
[2025/08/07 15:31:41.199 +08:00] [INFO] [server/rocksmq_impl.go:154] ["Start rocksmq "] ["max proc"=12] [parallism=2] ["lru cache"=2566541721]
[2025/08/07 15:31:41.227 +08:00] [INFO] [tso/tso.go:121] ["sync and save timestamp"] [last=2025/08/07 15:29:18.204 +08:00] [save=2025/08/07 15:31:44.227 +08:00] [next=2025/08/07 15:31:41.227 +08:00]
[2025/08/07 15:31:41.227 +08:00] [INFO] [server/rocksmq_retention.go:99] ["Rocksmq retention goroutine start!"]
[2025/08/07 15:31:41.242 +08:00] [INFO] [etcd/etcd_server.go:66] ["finish init Etcd config"] [path=] [data="D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data"]
[2025/08/07 15:31:41.242 +08:00] [INFO] [management/server.go:68] ["management listen"] [addr=:9091]
[2025/08/07 15:31:41.253 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.253 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.253 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.253 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.253 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.253 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.253 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.254 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.254 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.254 +08:00] [INFO] [rootcoord/root_coord.go:173] ["update rootcoord state"] [state=Abnormal]
[2025/08/07 15:31:41.258 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.258 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.258 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.258 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.258 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.258 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.258 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.258 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.260 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.261 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.261 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=rootcoord] [grpc.serverMaxSendSize=*********]
[2025/08/07 15:31:41.261 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=rootcoord] [grpc.serverMaxRecvSize=*********]
[2025/08/07 15:31:41.261 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=rootcoord] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.261 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=rootcoord] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.261 +08:00] [INFO] [rootcoord/service.go:177] ["init params done.."]
[2025/08/07 15:31:41.261 +08:00] [INFO] [rootcoord/service.go:196] ["etcd connect done ..."]
[2025/08/07 15:31:41.261 +08:00] [INFO] [rootcoord/service.go:251] ["start grpc "] [port=40000]
[2025/08/07 15:31:41.264 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.264 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.264 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.265 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=datacoord] [grpc.serverMaxSendSize=*********]
[2025/08/07 15:31:41.265 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=datacoord] [grpc.serverMaxRecvSize=*********]
[2025/08/07 15:31:41.265 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=datacoord] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.265 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=datacoord] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.265 +08:00] [INFO] [datacoord/service.go:117] ["create IndexCoord client for DataCoord"]
[2025/08/07 15:31:41.265 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.269 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.270 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.270 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.270 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.270 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.270 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.270 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.270 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.271 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.271 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.271 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=indexcoord] [grpc.serverMaxSendSize=*********]
[2025/08/07 15:31:41.271 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=indexcoord] [grpc.serverMaxRecvSize=*********]
[2025/08/07 15:31:41.271 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=indexcoord] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.271 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=indexcoord] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.271 +08:00] [INFO] [indexcoord/service.go:328] [IndexCoord] ["network address"=**************] ["network port"=40003]
[2025/08/07 15:31:41.274 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.274 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.274 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.274 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.274 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.274 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.274 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.274 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.275 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.275 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.275 +08:00] [INFO] [roles/roles.go:79] ["start clean local dir"] ["root path"="D:\\AAAAA-wq-work\\access\\milvus_data\\data\\querynode"]
[2025/08/07 15:31:41.276 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=querycoord] [grpc.serverMaxSendSize=*********]
[2025/08/07 15:31:41.276 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=querycoord] [grpc.serverMaxRecvSize=*********]
[2025/08/07 15:31:41.276 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=querycoord] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.276 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=querycoord] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.276 +08:00] [INFO] [querycoord/service.go:254] [network] [port=40001]
[2025/08/07 15:31:41.280 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.280 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.280 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.280 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.280 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.280 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.280 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.280 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.281 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.281 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.282 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=querynode] [grpc.serverMaxSendSize=*********]
[2025/08/07 15:31:41.282 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=querynode] [grpc.serverMaxRecvSize=*********]
[2025/08/07 15:31:41.282 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=querynode] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.282 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=querynode] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.282 +08:00] [INFO] [querynode/service.go:111] [QueryNode] [port=40002]
[2025/08/07 15:31:41.282 +08:00] [INFO] [querynode/service.go:127] ["QueryNode connect to etcd successfully"]
[2025/08/07 15:31:41.285 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.285 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.285 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.286 +08:00] [INFO] [roles/roles.go:79] ["start clean local dir"] ["root path"="D:\\AAAAA-wq-work\\access\\milvus_data\\data\\indexnode"]
[2025/08/07 15:31:41.286 +08:00] [INFO] [indexnode/indexnode.go:109] ["New IndexNode ..."]
[2025/08/07 15:31:41.286 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=datanode] [grpc.serverMaxSendSize=*********]
[2025/08/07 15:31:41.286 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=datanode] [grpc.serverMaxRecvSize=*********]
[2025/08/07 15:31:41.286 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=datanode] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.286 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=datanode] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.286 +08:00] [INFO] [datanode/service.go:256] ["DataNode address"] [address=**************:40006]
[2025/08/07 15:31:41.289 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.289 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.289 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.289 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.290 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.290 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.290 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.290 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.290 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.290 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.290 +08:00] [DEBUG] [proxy/multi_rate_limiter.go:331] ["RateLimiter register for rateType"] [rateType=DDLPartition] [rate=+inf] [burst=1.7976931348623157e+308]
[2025/08/07 15:31:41.290 +08:00] [DEBUG] [proxy/multi_rate_limiter.go:331] ["RateLimiter register for rateType"] [rateType=DMLDelete] [rate=+inf] [burst=1.7976931348623157e+308]
[2025/08/07 15:31:41.290 +08:00] [DEBUG] [proxy/service.go:122] ["create a new Proxy instance"] [module=Proxy] [state=Abnormal]
[2025/08/07 15:31:41.290 +08:00] [INFO] [proxy/service.go:368] ["init Proxy server"]
[2025/08/07 15:31:41.292 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=indexnode] [grpc.serverMaxSendSize=*********]
[2025/08/07 15:31:41.292 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=indexnode] [grpc.serverMaxRecvSize=*********]
[2025/08/07 15:31:41.292 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=indexnode] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.292 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=indexnode] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.292 +08:00] [INFO] [indexnode/service.go:90] [IndexNode] ["network address"=**************:40004] ["network port: "=40004]
[2025/08/07 15:31:41.296 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.298 +08:00] [DEBUG] [paramtable/grpc_param.go:156] [initServerMaxSendSize] [role=proxy] [grpc.serverMaxSendSize=67108864]
[2025/08/07 15:31:41.298 +08:00] [DEBUG] [paramtable/grpc_param.go:178] [initServerMaxRecvSize] [role=proxy] [grpc.serverMaxRecvSize=67108864]
[2025/08/07 15:31:41.298 +08:00] [WARN] [paramtable/grpc_param.go:192] ["Failed to parse grpc.gracefulStopTimeout, set to default"] [role=proxy] [grpc.gracefulStopTimeout=] [error="strconv.Atoi: parsing \"\": invalid syntax"]
[2025/08/07 15:31:41.298 +08:00] [DEBUG] [paramtable/grpc_param.go:201] [initGracefulStopTimeout] [role=proxy] [grpc.gracefulStopTimeout=30]
[2025/08/07 15:31:41.298 +08:00] [INFO] [proxy/service.go:397] ["Proxy init service's parameter table done"]
[2025/08/07 15:31:41.301 +08:00] [INFO] [proxy/service.go:399] ["Proxy init http server's parameter table done"]
[2025/08/07 15:31:41.301 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.301 +08:00] [INFO] [proxy/service.go:408] ["init Proxy's parameter table done"] ["internal address"=**************:19529] ["external address"=**************:19530]
[2025/08/07 15:31:41.301 +08:00] [INFO] [proxy/service.go:413] ["init Proxy's tracer done"] ["service name"="Proxy ip: **************, port: 19530"]
[2025/08/07 15:31:41.301 +08:00] [INFO] [proxy/service.go:314] ["Proxy internal server listen on tcp"] [port=19529]
[2025/08/07 15:31:41.301 +08:00] [INFO] [proxy/service.go:321] ["Proxy internal server already listen on tcp"] [port=19529]
[2025/08/07 15:31:41.302 +08:00] [INFO] [proxy/service.go:439] ["Proxy server listen on tcp"] [port=19530]
[2025/08/07 15:31:41.302 +08:00] [INFO] [proxy/service.go:443] ["Proxy server already listen on tcp"] [port=19530]
[2025/08/07 15:31:41.302 +08:00] [INFO] [proxy/service.go:354] ["create Proxy internal grpc server"] ["enforcement policy"="{\"MinTime\":5000000000,\"PermitWithoutStream\":true}"] ["server parameters"="{\"MaxConnectionIdle\":0,\"MaxConnectionAge\":0,\"MaxConnectionAgeGrace\":0,\"Time\":60000000000,\"Timeout\":10000000000}"]
[2025/08/07 15:31:41.302 +08:00] [INFO] [proxy/service.go:219] ["Get proxy rate limiter done"] [port=19530]
[2025/08/07 15:31:41.302 +08:00] [INFO] [proxy/service.go:290] ["create Proxy grpc server"] ["enforcement policy"="{\"MinTime\":5000000000,\"PermitWithoutStream\":true}"] ["server parameters"="{\"MaxConnectionIdle\":0,\"MaxConnectionAge\":0,\"MaxConnectionAgeGrace\":0,\"Time\":60000000000,\"Timeout\":10000000000}"]
[2025/08/07 15:31:41.302 +08:00] [INFO] [proxy/service.go:520] ["register Proxy http server"]
[2025/08/07 15:31:41.302 +08:00] [INFO] [proxy/service.go:527] ["create RootCoord client for Proxy"]
[2025/08/07 15:31:41.302 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.377 +08:00] [INFO] [rootcoord/service.go:202] ["grpc init done ..."]
[2025/08/07 15:31:41.377 +08:00] [INFO] [rootcoord/service.go:205] ["RootCoord start to create DataCoord client"]
[2025/08/07 15:31:41.377 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.377 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.377 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.393 +08:00] [INFO] [datanode/service.go:265] ["initializing RootCoord client for DataNode"]
[2025/08/07 15:31:41.393 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.393 +08:00] [INFO] [querynode/service.go:137] [QueryNode] [State=Initializing]
[2025/08/07 15:31:41.393 +08:00] [INFO] [querynode/query_node.go:311] ["QueryNode session info"] [metaPath=by-dev/meta]
[2025/08/07 15:31:41.393 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.397 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:41.397 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:41.397 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:41.397 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:41.397 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:41.397 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:41.397 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:41.398 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:41.398 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
[2025/08/07 15:31:41.398 +08:00] [DEBUG] [indexnode/indexnode.go:197] ["IndexNode init"] [State=Initializing]
[2025/08/07 15:31:41.398 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:41.659 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:41.659 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:41.659 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:41.659 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:41.659 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:41.659 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:41.770 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:41.770 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:41.770 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:41.770 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:41.770 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:41.770 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:41.880 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:41.880 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:41.880 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:41.880 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:41.880 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:41.880 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:41.991 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:41.991 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:41.991 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:41.991 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:41.991 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:41.991 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.116 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.116 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:42.116 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:42.116 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.116 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.116 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.227 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.227 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:42.227 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:42.227 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.227 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.227 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.339 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.339 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:42.339 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:42.339 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.339 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.339 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.448 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.448 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:42.448 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:42.448 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.448 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.448 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.560 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.560 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:42.560 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:42.560 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.560 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.560 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.671 +08:00] [DEBUG] [indexcoord/index_coord.go:400] ["get IndexCoord component states ..."]
[2025/08/07 15:31:42.671 +08:00] [DEBUG] [indexcoord/index_coord.go:420] ["IndexCoord GetComponentStates"] ["IndexCoord component state"="nodeID:-1 role:\"IndexCoord\" state_code:Abnormal "]
[2025/08/07 15:31:42.671 +08:00] [DEBUG] [querynode/impl.go:112] ["Get QueryNode component state done"] [stateCode=Initializing]
[2025/08/07 15:31:42.671 +08:00] [DEBUG] [datanode/data_node.go:561] ["DataNode current state"] [State=Abnormal]
[2025/08/07 15:31:42.671 +08:00] [DEBUG] [indexnode/indexnode.go:287] ["get IndexNode components states ..."]
[2025/08/07 15:31:42.671 +08:00] [DEBUG] [indexnode/indexnode.go:307] ["IndexNode Component states"] [State="nodeID:-1 role:\"indexnode\" "] [Status=] [SubcomponentStates=null]
[2025/08/07 15:31:42.750 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.750 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.750 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.750 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.750 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.750 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.750 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.752 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=9]
[2025/08/07 15:31:42.752 +08:00] [WARN] [sessionutil/session_util.go:302] ["Session Txn unsuccessful"] [key=id]
[2025/08/07 15:31:42.754 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=10]
[2025/08/07 15:31:42.754 +08:00] [INFO] [sessionutil/session_util.go:305] ["Session get serverID success"] [key=id] [ServerId=11]
[2025/08/07 15:31:42.755 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:42.755 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=datacoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=datacoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=datacoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=datacoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=datacoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=datacoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=datacoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=datacoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:31:42.758 +08:00] [INFO] [rootcoord/service.go:213] ["RootCoord start to create IndexCoord client"]
[2025/08/07 15:31:42.758 +08:00] [INFO] [sessionutil/session_util.go:207] ["Session try to connect to etcd"]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max send size"] [role=rootcoord] [grpc.clientMaxSendSize=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init client max recv size"] [role=rootcoord] [grpc.clientMaxRecvSize=104857600]
[2025/08/07 15:31:42.758 +08:00] [INFO] [sessionutil/session_util.go:222] ["Session connect to etcd success"]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init dial timeout"] [role=rootcoord] [grpc.client.dialTimeout=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive timeout"] [role=rootcoord] [grpc.client.keepAliveTimeout=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init keep alive time"] [role=rootcoord] [grpc.client.keepAliveTime=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max attempts"] [role=rootcoord] [grpc.client.maxMaxAttempts=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init initial back off"] [role=rootcoord] [grpc.client.initialBackOff=104857600]
[2025/08/07 15:31:42.758 +08:00] [DEBUG] [paramtable/grpc_param.go:263] ["Init max back off"] [role=rootcoord] [grpc.client.maxBackoff=104857600]
[2025/08/07 15:31:42.758 +08:00] [INFO] [proxy/service.go:533] ["create RootCoord client for Proxy done"]
[2025/08/07 15:31:42.758 +08:00] [INFO] [proxy/service.go:536] ["init RootCoord client for Proxy"]
[2025/08/07 15:31:42.758 +08:00] [INFO] [proxy/service.go:541] ["init RootCoord client for Proxy done"]
[2025/08/07 15:31:42.758 +08:00] [INFO] [proxy/service.go:543] ["Proxy wait for RootCoord to be healthy"]
[2025/08/07 15:31:42.758 +08:00] [INFO] [querycoord/service.go:167] ["QueryCoord try to wait for RootCoord ready"]
[2025/08/07 15:31:42.758 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.758 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.758 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.758 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.759 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.759 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.761 +08:00] [INFO] [paramtable/quota_param.go:787] ["init disk quota"] [diskQuota(MB)=+inf]
[2025/08/07 15:31:42.761 +08:00] [INFO] [paramtable/quota_param.go:802] ["init disk quota per DB"] [diskQuotaPerCollection(MB)=1.7976931348623157e+308]
[2025/08/07 15:31:42.762 +08:00] [INFO] [paramtable/component_param.go:1578] ["init segment max idle time"] [value=10m0s]
[2025/08/07 15:31:42.762 +08:00] [INFO] [paramtable/component_param.go:1583] ["init segment min size from idle to sealed"] [value=16]
[2025/08/07 15:31:42.762 +08:00] [INFO] [paramtable/component_param.go:1593] ["init segment max binlog file to sealed"] [value=32]
[2025/08/07 15:31:42.762 +08:00] [INFO] [paramtable/component_param.go:1588] ["init segment expansion rate"] [value=1.25]
[2025/08/07 15:31:42.762 +08:00] [INFO] [config/etcd_source.go:145] ["start refreshing configurations"]
[2025/08/07 15:31:42.762 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [module=Proxy] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=0): server ID mismatch"]
[2025/08/07 15:31:42.762 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=0): server ID mismatch"]
[2025/08/07 15:31:42.762 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.762 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.762 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.762 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.762 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=0): server ID mismatch"]
[2025/08/07 15:31:42.762 +08:00] [INFO] [sessionutil/session_util.go:532] ["SessionUtil GetSessions "] [prefix=rootcoord] [key=rootcoord] [address=**************:40000]
[2025/08/07 15:31:42.762 +08:00] [INFO] [client/client.go:109] ["RootCoordClient GetSessions success"] [address=**************:40000]
[2025/08/07 15:31:42.762 +08:00] [INFO] [paramtable/base_table.go:145] ["cannot find etcd.endpoints"]
[2025/08/07 15:31:42.762 +08:00] [INFO] [paramtable/hook_config.go:19] ["hook config"] [hook={}]
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetBlasThreshold][] Set faiss::distance_compute_blas_threshold to 16384
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetEarlyStopThreshold][] Set faiss::early_stop_threshold to 0
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetStatisticsLevel][] Set knowhere::STATISTICS_LEVEL to 0
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetBlasThreshold][] Set faiss::distance_compute_blas_threshold to 16384
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetEarlyStopThreshold][] Set faiss::early_stop_threshold to 0
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetStatisticsLevel][] Set knowhere::STATISTICS_LEVEL to 0
2025-08-07 15:31:42,764 DEBUG [default] [SERVER][operator()][] Config easylogging with yaml file: D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS expect simdType::AUTO
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS hook REF
2025-08-07 15:31:42,764 DEBUG [default] [SERVER][operator()][] Config easylogging with yaml file: D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml
2025-08-07 15:31:42,764 DEBUG [default] [SEGCORE][SegcoreSetSimdType][] set config simd_type: auto
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS expect simdType::AUTO
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS hook REF
2025-08-07 15:31:42,764 DEBUG [default] [SEGCORE][SetIndexSliceSize][] set config index slice size(byte): 16777216
2025-08-07 15:31:42,764 DEBUG [default] [SEGCORE][SetThreadCoreCoefficient][] set thread pool core coefficient: 10
[2025/08/07 15:31:43.772 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:43.772 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:43.772 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [module=Proxy] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:45.776 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [module=Proxy] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:45.776 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:45.776 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:49.789 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:49.789 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [module=Proxy] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:49.789 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.806 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [module=Proxy] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.806 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.806 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:31:57.807 +08:00] [WARN] [grpcclient/client.go:287] ["retry func failed"] [module=Proxy] ["retry time"=4] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:32:08.013 +08:00] [WARN] [retry/retry.go:39] ["start to reset connection because of specific reasons"] [module=Proxy] [client_role=rootcoord] [error="rpc error: code = Unknown desc = expected=%!s(int64=4), actual=%!s(int64=12): server ID mismatch"]
[2025/08/07 15:32:09.033 +08:00] [WARN] [grpcclient/client.go:367] ["fail to get grpc client"] [module=Proxy] [client_role=querycoord] [error="find no available querycoord, check querycoord state"]
[2025/08/07 15:32:09.033 +08:00] [WARN] [retry/retry.go:39] ["grpc client is nil, maybe fail to get client in the retry state"] [module=Proxy] [client_role=querycoord]
[2025/08/07 15:32:09.033 +08:00] [WARN] [grpcclient/client.go:291] ["fail to get grpc client in the retry state"] [module=Proxy] [client_role=querycoord] [error="find no available querycoord, check querycoord state"]
[2025/08/07 15:32:10.039 +08:00] [WARN] [retry/retry.go:39] ["grpc client is nil, maybe fail to get client in the retry state"] [module=Proxy] [client_role=querycoord]
[2025/08/07 15:32:10.039 +08:00] [WARN] [grpcclient/client.go:291] ["fail to get grpc client in the retry state"] [module=Proxy] [client_role=querycoord] [error="find no available querycoord, check querycoord state"]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量数据库服务模块
使用Milvus进行向量存储和检索，集成本地embedding模型
"""

try:
    from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
    from milvus import default_server
    import numpy as np
    MILVUS_AVAILABLE = True
    MILVUS_LITE_AVAILABLE = True
except ImportError as e:
    MILVUS_AVAILABLE = False
    MILVUS_LITE_AVAILABLE = False
    print(f"⚠️ Milvus未安装，向量检索功能将不可用: {e}")
    # 定义一个空的numpy模块以避免NameError
    class MockNumpy:
        def array(self, *args, **kwargs):
            return None
        ndarray = type(None)
    np = MockNumpy()
    # 定义空的default_server
    class MockDefaultServer:
        def start(self): pass
        def stop(self): pass
        @property
        def listen_port(self): return 19530
    default_server = MockDefaultServer()

import requests
import json
from typing import List, Dict, Any, Tuple, Optional
from sqlalchemy.orm import Session
from .database import SessionLocal
from .models import *
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VectorService:
    def __init__(self):
        # Embedding 模型服务配置
        self.embedding_url = "http://**************:18006/v1/embeddings"
        self.model_name = "Qwen3-Embedding-8B"
        self.api_key = "123456"
        self.vector_dim = 4096  # Qwen3-Embedding-8B的向量维度

        # Milvus连接配置
        self.milvus_host = "localhost"
        self.milvus_port = "19530"
        self.collection_name = "food_ingredients"
        self.milvus_server = None

        # 检查Milvus是否可用
        self.milvus_available = MILVUS_AVAILABLE
        self.collection = None

        if not self.milvus_available:
            logger.warning("Milvus不可用，向量检索功能将被禁用")
            return

        # 初始化Milvus连接和集合
        try:
            self._initialize_milvus()
        except Exception as e:
            logger.error(f"初始化Milvus失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            self.milvus_available = False

    def _initialize_milvus(self):
        """初始化Milvus连接和集合"""
        if not self.milvus_available:
            return

        # 连接到Milvus
        try:
            connections.connect("default", host=self.milvus_host, port=self.milvus_port)
            logger.info(f"成功连接到Milvus: {self.milvus_host}:{self.milvus_port}")
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            raise

        # 定义需要建立索引的数据库表和字段（仅合规查询相关的数据库）
        self.database_configs = {
            "加工助剂数据库": {
                "model": ProcessingAid,
                "fields": ["加工助剂名称"],
                "return_fields": ["加工助剂名称", "功能", "使用范围"]
            },
            "可用于食品的菌种数据库": {
                "model": FoodProbiotics,
                "fields": ["可用于食品的菌种名称"],
                "return_fields": ["可用于食品的菌种名称", "不适宜人群", "适用的食品类别", "标签要求"]
            },
            "可用于婴幼儿食品的菌种数据库": {
                "model": InfantProbiotics,
                "fields": ["可用于婴幼儿食品的菌种名称"],
                "return_fields": ["可用于婴幼儿食品的菌种名称", "适用食品类别", "对标签的标注要求"]
            },
            "三新食品原料数据库": {
                "model": NewFoodMaterial,
                "fields": ["新食品原料名称"],
                "return_fields": ["新食品原料名称", "适宜人群", "推荐食用量", "使用范围和最大使用量"]
            },
            "添加剂数据库": {
                "model": FoodAdditive,
                "fields": ["添加剂名称"],
                "return_fields": ["添加剂名称", "INS号", "功能", "食品分类号", "食品名称", "最大使用量g_kg", "备注"]
            },
            "药食同源名单": {
                "model": FoodMedicineSameOrigin,
                "fields": ["既是食品又是药品的物品名称"],
                "return_fields": ["既是食品又是药品的物品名称", "适用食品的类别", "对标签的标注要求"]
            },
            "营养强化剂数据库": {
                "model": NutrientFortifier,
                "fields": ["营养强化剂名称", "化合物来源"],
                "return_fields": ["营养强化剂名称", "化合物来源", "应用范围", "适用食品", "在适用食品中的要求"]
            },
            "胶基数据库": {
                "model": GumBase,
                "fields": ["胶基配料"],
                "return_fields": ["胶基配料", "胶基配料的英文名称", "所属类别"]
            },
            "酶制剂数据库": {
                "model": EnzymePreparation,
                "fields": ["酶制剂名称", "来源", "供体"],
                "return_fields": ["酶制剂名称", "来源", "供体"]
            },
            "香精香料数据库": {
                "model": FlavorSpice,
                "fields": ["香料中文名称"],
                "return_fields": ["香料中文名称", "FEMA编号"]
            },
            "中国食物成分表": {
                "model": FoodComposition,
                "fields": ["名称"],
                "return_fields": ["名称"]
            },
            "食品原料数据库": {
                "model": FoodIngredient,
                "fields": ["食品原料名称"],
                "return_fields": ["食品原料名称", "基本信息", "来源", "实施日期", "有效性", "备注"]
            }
        }

        # 创建或加载集合
        self._create_or_load_collection()

    def _create_or_load_collection(self):
        """创建或加载Milvus集合"""
        try:
            # 检查集合是否存在
            if utility.has_collection(self.collection_name):
                logger.info(f"集合 {self.collection_name} 已存在，加载中...")
                self.collection = Collection(self.collection_name)

                # 确保集合被加载到内存中
                if not self.collection.has_index():
                    logger.warning("集合没有索引，创建索引...")
                    self._create_index_for_existing_collection()

                # 加载集合到内存
                self.collection.load()
                logger.info(f"成功加载集合到内存，包含 {self.collection.num_entities} 条记录")
            else:
                logger.info(f"创建新集合: {self.collection_name}")
                self._create_collection()
                self._build_index_and_load_data()
        except Exception as e:
            logger.error(f"创建或加载集合失败: {e}")
            raise

    def _create_collection(self):
        """创建Milvus集合"""
        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="text", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="database_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="field_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="data_json", dtype=DataType.VARCHAR, max_length=5000),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.vector_dim)
        ]

        # 创建集合schema
        schema = CollectionSchema(fields, f"食品配料向量数据库")

        # 创建集合
        self.collection = Collection(self.collection_name, schema)
        logger.info(f"成功创建集合: {self.collection_name}")

    def _create_index_for_existing_collection(self):
        """为现有集合创建向量索引"""
        try:
            logger.info("为现有集合创建向量索引...")

            # 创建向量索引（匹配初始化脚本中的参数）
            index_params = {
                "metric_type": "L2",  # L2距离度量
                "index_type": "IVF_FLAT",
                "params": {"nlist": 1024}  # 匹配初始化脚本中的nlist值
            }

            self.collection.create_index("vector", index_params)
            logger.info("向量索引创建完成")

        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            # 即使索引创建失败，也继续尝试加载集合

    def _build_index_and_load_data(self):
        """构建索引并加载数据到Milvus"""
        logger.info("开始构建向量索引并加载数据...")

        all_data = []

        # 获取数据库会话
        db = SessionLocal()
        try:
            # 遍历所有数据库配置
            for db_name, config in self.database_configs.items():
                logger.info(f"处理数据库: {db_name}")

                model_class = config["model"]
                fields = config["fields"]
                return_fields = config["return_fields"]

                # 查询所有记录
                records = db.query(model_class).all()

                for record in records:
                    for field in fields:
                        field_value = getattr(record, field, None)
                        if field_value and str(field_value).strip():
                            text = str(field_value).strip()

                            # 构建返回数据
                            return_data = {}
                            for return_field in return_fields:
                                return_data[return_field] = getattr(record, return_field, None)

                            all_data.append({
                                "text": text,
                                "database_name": db_name,
                                "field_name": field,
                                "data_json": json.dumps(return_data, ensure_ascii=False)
                            })

            if not all_data:
                logger.warning("没有找到可索引的数据")
                return

            logger.info(f"总共收集到 {len(all_data)} 条数据，开始生成向量...")

            # 批量获取向量
            texts = [item["text"] for item in all_data]
            vectors = self._get_embeddings(texts)

            if vectors is None:
                logger.error("获取向量失败")
                return

            # 准备插入数据
            insert_data = []
            for i, item in enumerate(all_data):
                insert_data.append([
                    item["text"],
                    item["database_name"],
                    item["field_name"],
                    item["data_json"],
                    vectors[i].tolist()
                ])

            # 插入数据到Milvus
            logger.info("开始插入数据到Milvus...")
            self.collection.insert([
                [item[0] for item in insert_data],  # text
                [item[1] for item in insert_data],  # database_name
                [item[2] for item in insert_data],  # field_name
                [item[3] for item in insert_data],  # data_json
                [item[4] for item in insert_data]   # vector
            ])

            # 创建索引
            logger.info("创建向量索引...")
            index_params = {
                "metric_type": "IP",  # 内积相似度
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            self.collection.create_index("vector", index_params)

            # 加载集合到内存
            self.collection.load()

            logger.info(f"成功构建索引并加载 {len(all_data)} 条数据")

        except Exception as e:
            logger.error(f"构建索引时发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
        finally:
            db.close()
    
    def _get_embeddings(self, texts: List[str]):
        """调用本地embedding模型获取文本向量"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model_name,
                "input": texts
            }
            
            response = requests.post(
                self.embedding_url,
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                embeddings = []
                for item in result["data"]:
                    embeddings.append(item["embedding"])
                return np.array(embeddings)
            else:
                logger.error(f"Embedding API调用失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"调用embedding模型时发生错误: {e}")
            return None
    
    def search_similar(self, query_text: str, top_k: int = 3, similarity_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """在所有数据库中搜索相似文本"""
        if not self.milvus_available or self.collection is None:
            logger.warning("Milvus不可用，无法进行向量检索")
            return []

        try:
            # 确保集合被加载到内存中
            try:
                self.collection.load()
                logger.debug("集合已加载到内存")
            except Exception as e:
                logger.warning(f"加载集合时出现警告: {e}")

            # 获取查询文本的向量
            query_vector = self._get_embeddings([query_text])
            if query_vector is None:
                return []

            # 搜索参数（匹配初始化脚本中的L2距离度量）
            search_params = {
                "metric_type": "L2",
                "params": {"nprobe": 10}
            }

            # 执行向量搜索（适配现有集合结构：只有id和vector字段）
            results = self.collection.search(
                data=[query_vector[0].tolist()],
                anns_field="vector",
                param=search_params,
                limit=top_k * 2,  # 搜索更多结果以便过滤
                output_fields=["id"]  # 现有集合只有id和vector字段
            )

            all_results = []

            # 处理搜索结果（适配现有集合结构和L2距离度量）
            for hits in results:
                for hit in hits:
                    distance = float(hit.score)  # L2距离，越小越相似

                    # 将L2距离转换为相似度百分比（距离越小，相似度越高）
                    # 这里使用一个简单的转换公式，可以根据实际情况调整
                    similarity = max(0, 1 - distance / 2.0)  # 假设距离2.0对应0%相似度

                    # L2距离阈值：距离小于某个值才认为相似
                    # 相似度50%对应L2距离约1.0
                    distance_threshold = 2.0 * (1 - similarity_threshold)

                    if distance <= distance_threshold:
                        # 现有集合只有id字段，我们需要根据id查找原始数据
                        vector_id = hit.entity.get("id")

                        # 尝试根据id查找对应的原始数据
                        ingredient_data = self._get_ingredient_by_vector_id(vector_id)

                        if ingredient_data:
                            result = {
                                "配料名称": ingredient_data.get("name", f"向量ID_{vector_id}"),
                                "数据库来源": ingredient_data.get("database", "向量数据库"),
                                "相似度": similarity,
                                "查询结果": ingredient_data.get("data", {}),
                                "匹配字段": ingredient_data.get("field", "向量匹配"),
                                "L2距离": distance
                            }
                            all_results.append(result)

            # 按相似度排序并去重
            all_results.sort(key=lambda x: x["相似度"], reverse=True)

            # 去重（基于配料名称和数据库来源）
            seen = set()
            unique_results = []
            for result in all_results:
                key = f"{result['配料名称']}_{result['数据库来源']}"
                if key not in seen:
                    seen.add(key)
                    unique_results.append(result)
                    if len(unique_results) >= top_k:
                        break

            return unique_results

        except Exception as e:
            logger.error(f"Milvus向量搜索时发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return []

    def _get_ingredient_by_vector_id(self, vector_id: int) -> dict:
        """根据向量ID从映射表查找对应的配料信息"""
        try:
            # 从milvus_mapping表中查询原始数据
            db = SessionLocal()
            try:
                from sqlalchemy import text

                # 查询映射表获取原始数据
                query = text("SELECT original_data FROM milvus_mapping WHERE milvus_id = :vector_id")
                result = db.execute(query, {"vector_id": vector_id}).fetchone()

                if result:
                    # 原始数据是直接存储的配料名称文本
                    ingredient_name = str(result[0]).strip()

                    # 根据配料名称推断可能的数据库来源
                    database_source = self._infer_database_source(ingredient_name)

                    return {
                        "name": ingredient_name,
                        "database": database_source,
                        "field": "语义匹配",
                        "data": {
                            "配料名称": ingredient_name,
                            "数据库来源": database_source,
                            "匹配类型": "语义相似度匹配",
                            "向量ID": vector_id,
                            "匹配字段": "向量检索"
                        }
                    }
                else:
                    # 映射表中没有找到对应记录
                    logger.warning(f"映射表中未找到向量ID {vector_id} 的记录")
                    return {
                        "name": f"未知配料_{vector_id}",
                        "database": "向量数据库",
                        "field": "向量匹配",
                        "data": {
                            "向量ID": vector_id,
                            "匹配类型": "向量相似度匹配",
                            "说明": "映射表中未找到对应记录"
                        }
                    }

            finally:
                db.close()

        except Exception as e:
            logger.error(f"查找向量ID {vector_id} 对应数据失败: {e}")
            return {
                "name": f"查询失败_{vector_id}",
                "database": "向量数据库",
                "field": "错误",
                "data": {
                    "向量ID": vector_id,
                    "匹配类型": "向量相似度匹配",
                    "错误信息": str(e)
                }
            }

    def _infer_database_source(self, ingredient_name: str) -> str:
        """根据配料名称推断可能的数据库来源"""
        try:
            # 定义关键词到数据库的映射
            database_keywords = {
                "营养强化剂数据库": [
                    "维生素", "VE", "VC", "VA", "VD", "VB", "叶酸", "烟酸", "泛酸",
                    "生育酚", "抗坏血酸", "胡萝卜素", "核黄素", "硫胺素"
                ],
                "添加剂数据库": [
                    "柠檬酸", "苹果酸", "乳酸", "醋酸", "山梨酸", "苯甲酸", "亚硝酸",
                    "磷酸", "碳酸", "硫酸", "氯化", "硫酸钠", "磷酸钠", "柠檬酸钠"
                ],
                "香精香料数据库": [
                    "香料", "香精", "香草", "薄荷", "柠檬", "橙子", "草莓", "香兰素",
                    "乙基麦芽酚", "丁香", "肉桂", "茴香"
                ],
                "加工助剂数据库": [
                    "氨水", "液氨", "甘油", "丙三醇", "盐酸", "氢氧化钠", "碳酸钠"
                ],
                "酶制剂数据库": [
                    "酶", "淀粉酶", "蛋白酶", "脂肪酶", "纤维素酶", "果胶酶"
                ],
                "药食同源名单": [
                    "人参", "当归", "枸杞", "大枣", "山药", "茯苓", "甘草", "陈皮"
                ],
                "三新食品原料数据库": [
                    "螺旋藻", "破壁灵芝孢子粉", "蛹虫草", "玛咖粉"
                ]
            }

            # 检查配料名称中是否包含关键词
            ingredient_lower = ingredient_name.lower()
            for database, keywords in database_keywords.items():
                for keyword in keywords:
                    if keyword.lower() in ingredient_lower:
                        return database

            # 默认返回食品原料数据库
            return "食品原料数据库"

        except Exception as e:
            logger.error(f"推断数据库来源失败: {e}")
            return "未知数据库"
    
    def rebuild_all_indexes(self):
        """重建所有索引"""
        if not self.milvus_available:
            logger.warning("Milvus不可用，无法重建向量索引")
            return

        logger.info("开始重建所有向量索引...")

        try:
            # 删除现有集合
            if utility.has_collection(self.collection_name):
                utility.drop_collection(self.collection_name)
                logger.info(f"已删除现有集合: {self.collection_name}")

            # 重新创建集合
            self._create_collection()
            self._build_index_and_load_data()

            logger.info("所有向量索引重建完成")

        except Exception as e:
            logger.error(f"重建索引失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def cleanup(self):
        """清理资源，停止Milvus Lite服务"""
        if self.milvus_server and self.milvus_available:
            try:
                logger.info("正在停止Milvus Lite服务...")
                default_server.stop()
                logger.info("Milvus Lite服务已停止")
            except Exception as e:
                logger.error(f"停止Milvus Lite服务失败: {e}")

    def __del__(self):
        """析构函数，确保资源清理"""
        self.cleanup()

# 全局向量服务实例
vector_service = None

def get_vector_service() -> VectorService:
    """获取向量服务实例"""
    global vector_service
    if vector_service is None:
        vector_service = VectorService()
    return vector_service

2025/08/07-15:31:41.201606 6f64 RocksDB version: 6.26.1
2025/08/07-15:31:41.201654 6f64 Git sha 0
2025/08/07-15:31:41.201663 6f64 Compile date 2021-12-13 18:23:52
2025/08/07-15:31:41.201670 6f64 DB SUMMARY
2025/08/07-15:31:41.201675 6f64 DB Session ID:  AS05M3QP3EDL178NAAEN
2025/08/07-15:31:41.201778 6f64 CURRENT file:  CURRENT
2025/08/07-15:31:41.201786 6f64 IDENTITY file:  IDENTITY
2025/08/07-15:31:41.201797 6f64 MANIFEST file:  MANIFEST-000004 size: 57 Bytes
2025/08/07-15:31:41.201803 6f64 SST files in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv dir, Total Num: 0, files: 
2025/08/07-15:31:41.201809 6f64 Write Ahead Log file in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv: 000005.log size: 1680 ; 
2025/08/07-15:31:41.201815 6f64                         Options.error_if_exists: 0
2025/08/07-15:31:41.202118 6f64                       Options.create_if_missing: 1
2025/08/07-15:31:41.202124 6f64                         Options.paranoid_checks: 1
2025/08/07-15:31:41.202126 6f64             Options.flush_verify_memtable_count: 1
2025/08/07-15:31:41.202128 6f64                               Options.track_and_verify_wals_in_manifest: 0
2025/08/07-15:31:41.202131 6f64                                     Options.env: 0000022cd13db880
2025/08/07-15:31:41.202133 6f64                                      Options.fs: WinFS
2025/08/07-15:31:41.202136 6f64                                Options.info_log: 0000022cd1467ca0
2025/08/07-15:31:41.202138 6f64                Options.max_file_opening_threads: 16
2025/08/07-15:31:41.202140 6f64                              Options.statistics: 0000000000000000
2025/08/07-15:31:41.202142 6f64                               Options.use_fsync: 0
2025/08/07-15:31:41.202144 6f64                       Options.max_log_file_size: 0
2025/08/07-15:31:41.202146 6f64                  Options.max_manifest_file_size: 1073741824
2025/08/07-15:31:41.202148 6f64                   Options.log_file_time_to_roll: 0
2025/08/07-15:31:41.202150 6f64                       Options.keep_log_file_num: 1000
2025/08/07-15:31:41.202152 6f64                    Options.recycle_log_file_num: 0
2025/08/07-15:31:41.202154 6f64                         Options.allow_fallocate: 1
2025/08/07-15:31:41.202156 6f64                        Options.allow_mmap_reads: 0
2025/08/07-15:31:41.202158 6f64                       Options.allow_mmap_writes: 0
2025/08/07-15:31:41.202160 6f64                        Options.use_direct_reads: 0
2025/08/07-15:31:41.202162 6f64                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/07-15:31:41.202164 6f64          Options.create_missing_column_families: 0
2025/08/07-15:31:41.202166 6f64                              Options.db_log_dir: 
2025/08/07-15:31:41.202168 6f64                                 Options.wal_dir: 
2025/08/07-15:31:41.202171 6f64                Options.table_cache_numshardbits: 6
2025/08/07-15:31:41.202173 6f64                         Options.WAL_ttl_seconds: 0
2025/08/07-15:31:41.202175 6f64                       Options.WAL_size_limit_MB: 0
2025/08/07-15:31:41.202177 6f64                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/07-15:31:41.202179 6f64             Options.manifest_preallocation_size: 4194304
2025/08/07-15:31:41.202181 6f64                     Options.is_fd_close_on_exec: 1
2025/08/07-15:31:41.202183 6f64                   Options.advise_random_on_open: 1
2025/08/07-15:31:41.202185 6f64                   Options.experimental_mempurge_threshold: 0.000000
2025/08/07-15:31:41.202195 6f64                    Options.db_write_buffer_size: 0
2025/08/07-15:31:41.202198 6f64                    Options.write_buffer_manager: 0000022cd1440610
2025/08/07-15:31:41.202201 6f64         Options.access_hint_on_compaction_start: 1
2025/08/07-15:31:41.202203 6f64  Options.new_table_reader_for_compaction_inputs: 0
2025/08/07-15:31:41.202205 6f64           Options.random_access_max_buffer_size: 1048576
2025/08/07-15:31:41.202208 6f64                      Options.use_adaptive_mutex: 0
2025/08/07-15:31:41.202210 6f64                            Options.rate_limiter: 0000000000000000
2025/08/07-15:31:41.202229 6f64     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/07-15:31:41.202232 6f64                       Options.wal_recovery_mode: 2
2025/08/07-15:31:41.202234 6f64                  Options.enable_thread_tracking: 0
2025/08/07-15:31:41.202236 6f64                  Options.enable_pipelined_write: 0
2025/08/07-15:31:41.202238 6f64                  Options.unordered_write: 0
2025/08/07-15:31:41.202240 6f64         Options.allow_concurrent_memtable_write: 1
2025/08/07-15:31:41.202243 6f64      Options.enable_write_thread_adaptive_yield: 1
2025/08/07-15:31:41.202245 6f64             Options.write_thread_max_yield_usec: 100
2025/08/07-15:31:41.202247 6f64            Options.write_thread_slow_yield_usec: 3
2025/08/07-15:31:41.202249 6f64                               Options.row_cache: None
2025/08/07-15:31:41.202251 6f64                              Options.wal_filter: None
2025/08/07-15:31:41.202253 6f64             Options.avoid_flush_during_recovery: 0
2025/08/07-15:31:41.202255 6f64             Options.allow_ingest_behind: 0
2025/08/07-15:31:41.202257 6f64             Options.preserve_deletes: 0
2025/08/07-15:31:41.202259 6f64             Options.two_write_queues: 0
2025/08/07-15:31:41.202261 6f64             Options.manual_wal_flush: 0
2025/08/07-15:31:41.202263 6f64             Options.atomic_flush: 0
2025/08/07-15:31:41.202265 6f64             Options.avoid_unnecessary_blocking_io: 0
2025/08/07-15:31:41.202267 6f64                 Options.persist_stats_to_disk: 0
2025/08/07-15:31:41.202269 6f64                 Options.write_dbid_to_manifest: 0
2025/08/07-15:31:41.202271 6f64                 Options.log_readahead_size: 0
2025/08/07-15:31:41.202273 6f64                 Options.file_checksum_gen_factory: Unknown
2025/08/07-15:31:41.202276 6f64                 Options.best_efforts_recovery: 0
2025/08/07-15:31:41.202278 6f64                Options.max_bgerror_resume_count: 2147483647
2025/08/07-15:31:41.202280 6f64            Options.bgerror_resume_retry_interval: 1000000
2025/08/07-15:31:41.202282 6f64             Options.allow_data_in_errors: 0
2025/08/07-15:31:41.202284 6f64             Options.db_host_id: __hostname__
2025/08/07-15:31:41.202286 6f64             Options.max_background_jobs: 2
2025/08/07-15:31:41.202288 6f64             Options.max_background_compactions: -1
2025/08/07-15:31:41.202290 6f64             Options.max_subcompactions: 1
2025/08/07-15:31:41.202292 6f64             Options.avoid_flush_during_shutdown: 0
2025/08/07-15:31:41.202294 6f64           Options.writable_file_max_buffer_size: 1048576
2025/08/07-15:31:41.202296 6f64             Options.delayed_write_rate : 16777216
2025/08/07-15:31:41.202299 6f64             Options.max_total_wal_size: 0
2025/08/07-15:31:41.202301 6f64             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/07-15:31:41.202303 6f64                   Options.stats_dump_period_sec: 600
2025/08/07-15:31:41.202305 6f64                 Options.stats_persist_period_sec: 600
2025/08/07-15:31:41.202307 6f64                 Options.stats_history_buffer_size: 1048576
2025/08/07-15:31:41.202309 6f64                          Options.max_open_files: -1
2025/08/07-15:31:41.202311 6f64                          Options.bytes_per_sync: 0
2025/08/07-15:31:41.202313 6f64                      Options.wal_bytes_per_sync: 0
2025/08/07-15:31:41.202315 6f64                   Options.strict_bytes_per_sync: 0
2025/08/07-15:31:41.202317 6f64       Options.compaction_readahead_size: 0
2025/08/07-15:31:41.202319 6f64                  Options.max_background_flushes: 1
2025/08/07-15:31:41.202321 6f64 Compression algorithms supported:
2025/08/07-15:31:41.202324 6f64 	kZSTD supported: 1
2025/08/07-15:31:41.202326 6f64 	kXpressCompression supported: 0
2025/08/07-15:31:41.202328 6f64 	kBZip2Compression supported: 1
2025/08/07-15:31:41.202330 6f64 	kZSTDNotFinalCompression supported: 1
2025/08/07-15:31:41.202332 6f64 	kLZ4Compression supported: 1
2025/08/07-15:31:41.202334 6f64 	kZlibCompression supported: 1
2025/08/07-15:31:41.202347 6f64 	kLZ4HCCompression supported: 1
2025/08/07-15:31:41.202349 6f64 	kSnappyCompression supported: 1
2025/08/07-15:31:41.202355 6f64 Fast CRC32 supported: Supported on x86
2025/08/07-15:31:41.202697 6f64 [db/version_set.cc:4847] Recovering from manifest file: D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv/MANIFEST-000004
2025/08/07-15:31:41.202790 6f64 [db/column_family.cc:609] --------------- Options for column family [default]:
2025/08/07-15:31:41.202794 6f64               Options.comparator: leveldb.BytewiseComparator
2025/08/07-15:31:41.202796 6f64           Options.merge_operator: None
2025/08/07-15:31:41.202798 6f64        Options.compaction_filter: None
2025/08/07-15:31:41.202800 6f64        Options.compaction_filter_factory: None
2025/08/07-15:31:41.202802 6f64  Options.sst_partitioner_factory: None
2025/08/07-15:31:41.202804 6f64         Options.memtable_factory: SkipListFactory
2025/08/07-15:31:41.202806 6f64            Options.table_factory: BlockBasedTable
2025/08/07-15:31:41.202825 6f64            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0000022cd144ecc0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0000022cd13f2b60
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 2566541721
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/07-15:31:41.202828 6f64        Options.write_buffer_size: 67108864
2025/08/07-15:31:41.202830 6f64  Options.max_write_buffer_number: 2
2025/08/07-15:31:41.202834 6f64        Options.compression[0]: NoCompression
2025/08/07-15:31:41.202836 6f64        Options.compression[1]: NoCompression
2025/08/07-15:31:41.202838 6f64        Options.compression[2]: ZSTD
2025/08/07-15:31:41.202840 6f64        Options.compression[3]: ZSTD
2025/08/07-15:31:41.202842 6f64        Options.compression[4]: ZSTD
2025/08/07-15:31:41.202844 6f64        Options.compression[5]: ZSTD
2025/08/07-15:31:41.202846 6f64        Options.compression[6]: ZSTD
2025/08/07-15:31:41.202848 6f64                  Options.bottommost_compression: Disabled
2025/08/07-15:31:41.202850 6f64       Options.prefix_extractor: nullptr
2025/08/07-15:31:41.202852 6f64   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/07-15:31:41.202854 6f64             Options.num_levels: 7
2025/08/07-15:31:41.202856 6f64        Options.min_write_buffer_number_to_merge: 1
2025/08/07-15:31:41.202858 6f64     Options.max_write_buffer_number_to_maintain: 0
2025/08/07-15:31:41.202860 6f64     Options.max_write_buffer_size_to_maintain: 0
2025/08/07-15:31:41.202862 6f64            Options.bottommost_compression_opts.window_bits: -14
2025/08/07-15:31:41.202864 6f64                  Options.bottommost_compression_opts.level: 32767
2025/08/07-15:31:41.202867 6f64               Options.bottommost_compression_opts.strategy: 0
2025/08/07-15:31:41.202869 6f64         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/07-15:31:41.202871 6f64         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:31:41.202873 6f64         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/07-15:31:41.202875 6f64                  Options.bottommost_compression_opts.enabled: false
2025/08/07-15:31:41.202878 6f64         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:31:41.202881 6f64            Options.compression_opts.window_bits: -14
2025/08/07-15:31:41.202883 6f64                  Options.compression_opts.level: 32767
2025/08/07-15:31:41.202885 6f64               Options.compression_opts.strategy: 0
2025/08/07-15:31:41.202887 6f64         Options.compression_opts.max_dict_bytes: 0
2025/08/07-15:31:41.202889 6f64         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:31:41.202891 6f64         Options.compression_opts.parallel_threads: 1
2025/08/07-15:31:41.202893 6f64                  Options.compression_opts.enabled: false
2025/08/07-15:31:41.202895 6f64         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:31:41.202897 6f64      Options.level0_file_num_compaction_trigger: 4
2025/08/07-15:31:41.202899 6f64          Options.level0_slowdown_writes_trigger: 20
2025/08/07-15:31:41.202901 6f64              Options.level0_stop_writes_trigger: 36
2025/08/07-15:31:41.202903 6f64                   Options.target_file_size_base: 67108864
2025/08/07-15:31:41.202905 6f64             Options.target_file_size_multiplier: 1
2025/08/07-15:31:41.202907 6f64                Options.max_bytes_for_level_base: 268435456
2025/08/07-15:31:41.202910 6f64 Options.level_compaction_dynamic_level_bytes: 0
2025/08/07-15:31:41.202912 6f64          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/07-15:31:41.202914 6f64 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/07-15:31:41.202916 6f64 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/07-15:31:41.202918 6f64 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/07-15:31:41.202920 6f64 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/07-15:31:41.202922 6f64 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/07-15:31:41.202924 6f64 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/07-15:31:41.202926 6f64 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/07-15:31:41.202928 6f64       Options.max_sequential_skip_in_iterations: 8
2025/08/07-15:31:41.202931 6f64                    Options.max_compaction_bytes: 1677721600
2025/08/07-15:31:41.202933 6f64                        Options.arena_block_size: 1048576
2025/08/07-15:31:41.202935 6f64   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/07-15:31:41.202937 6f64   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/07-15:31:41.202939 6f64       Options.rate_limit_delay_max_milliseconds: 100
2025/08/07-15:31:41.202941 6f64                Options.disable_auto_compactions: 0
2025/08/07-15:31:41.202943 6f64                        Options.compaction_style: kCompactionStyleLevel
2025/08/07-15:31:41.202946 6f64                          Options.compaction_pri: kMinOverlappingRatio
2025/08/07-15:31:41.202948 6f64 Options.compaction_options_universal.size_ratio: 1
2025/08/07-15:31:41.202950 6f64 Options.compaction_options_universal.min_merge_width: 2
2025/08/07-15:31:41.202952 6f64 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/07-15:31:41.202954 6f64 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/07-15:31:41.202956 6f64 Options.compaction_options_universal.compression_size_percent: -1
2025/08/07-15:31:41.202958 6f64 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/07-15:31:41.202960 6f64 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/07-15:31:41.202963 6f64 Options.compaction_options_fifo.allow_compaction: 0
2025/08/07-15:31:41.202967 6f64                   Options.table_properties_collectors: 
2025/08/07-15:31:41.202970 6f64                   Options.inplace_update_support: 0
2025/08/07-15:31:41.202972 6f64                 Options.inplace_update_num_locks: 10000
2025/08/07-15:31:41.202974 6f64               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/07-15:31:41.202976 6f64               Options.memtable_whole_key_filtering: 0
2025/08/07-15:31:41.202992 6f64   Options.memtable_huge_page_size: 0
2025/08/07-15:31:41.202995 6f64                           Options.bloom_locality: 0
2025/08/07-15:31:41.202997 6f64                    Options.max_successive_merges: 0
2025/08/07-15:31:41.202999 6f64                Options.optimize_filters_for_hits: 0
2025/08/07-15:31:41.203001 6f64                Options.paranoid_file_checks: 0
2025/08/07-15:31:41.203003 6f64                Options.force_consistency_checks: 1
2025/08/07-15:31:41.203005 6f64                Options.report_bg_io_stats: 0
2025/08/07-15:31:41.203007 6f64                               Options.ttl: 2592000
2025/08/07-15:31:41.203009 6f64          Options.periodic_compaction_seconds: 0
2025/08/07-15:31:41.203011 6f64                       Options.enable_blob_files: false
2025/08/07-15:31:41.203013 6f64                           Options.min_blob_size: 0
2025/08/07-15:31:41.203015 6f64                          Options.blob_file_size: 268435456
2025/08/07-15:31:41.203018 6f64                   Options.blob_compression_type: NoCompression
2025/08/07-15:31:41.203020 6f64          Options.enable_blob_garbage_collection: false
2025/08/07-15:31:41.203022 6f64      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/07-15:31:41.203024 6f64 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/07-15:31:41.204302 6f64 [db/version_set.cc:4887] Recovered from manifest file:D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 6, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/07-15:31:41.204309 6f64 [db/version_set.cc:4902] Column family [default] (ID 0), log number is 0
2025/08/07-15:31:41.204515 6f64 [db/version_set.cc:4385] Creating manifest 8
2025/08/07-15:31:41.206981 6f64 EVENT_LOG_v1 {"time_micros": 1754551901206971, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/08/07-15:31:41.206988 6f64 [db/db_impl/db_impl_open.cc:876] Recovering log #5 mode 2
2025/08/07-15:31:41.208518 6f64 EVENT_LOG_v1 {"time_micros": 1754551901208495, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 9, "file_size": 1670, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 700, "index_size": 49, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1338, "raw_average_key_size": 40, "raw_value_size": 184, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 33, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754551901, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "37fae3b1-7360-11f0-919d-10ffe00301a5", "db_session_id": "AS05M3QP3EDL178NAAEN", "orig_file_number": 9}}
2025/08/07-15:31:41.208574 6f64 [db/version_set.cc:4385] Creating manifest 10
2025/08/07-15:31:41.211818 6f64 EVENT_LOG_v1 {"time_micros": 1754551901211815, "job": 1, "event": "recovery_finished"}
2025/08/07-15:31:41.212521 6f64 [file/delete_scheduler.cc:73] Deleted file D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv/000005.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/07-15:31:41.215187 6f64 [db/db_impl/db_impl_open.cc:1786] SstFileManager instance 0000022cd1478e20
2025/08/07-15:31:41.215274 6f64 DB pointer 0000022cd147aa90
2025/08/07-15:31:41.215605 50e0 [db/db_impl/db_impl.cc:1004] ------- DUMPING STATS -------
2025/08/07-15:31:41.215646 50e0 [db/db_impl/db_impl.cc:1006] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.63 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.00              0.00         1    0.001       0      0       0.0       0.0
 Sum      1/0    1.63 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.00              0.00         1    0.001       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.00              0.00         1    0.001       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.1      0.00              0.00         1    0.001       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.12 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.12 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x22cd13f2b60#28564 capacity: 2.39 GB collections: 1 last_copies: 0 last_secs: 2.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

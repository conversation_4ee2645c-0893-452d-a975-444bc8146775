from pymilvus import connections, Collection

# 连接到Milvus
connections.connect("default", host="localhost", port="19530")

# 获取集合
collection = Collection("food_ingredients")

# 打印集合信息
print("集合名称：", collection.name)
print("集合描述：", collection.description)
print("集合架构：", collection.schema)
print("集合数据量：", collection.num_entities)

# 查询前5条数据的id
results = collection.query(
    expr="id >= 0",  # 查询条件，假设id是从0开始的整数
    offset=0,
    limit=5,
    output_fields=["id"]  # 输出id字段
)

# 打印查询结果
for result in results:
    print("id：", result.get("id"))
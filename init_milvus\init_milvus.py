import mysql.connector
import sys
sys.path.append('/usr/local/lib/python3.8/site-packages')
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, list_collections
import requests
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO)

# MySQL 配置
mysql_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'food_dataset'
}

# Milvus 配置
milvus_config = {
    'host': 'localhost',
    'port': '19530'
}

# Embedding 模型服务配置
embedding_config = {
    'url': 'http://**************:18006/v1/embeddings',
    'model': 'Qwen3-Embedding-8B',
    'apikey': '123456'
}

# Milvus 集合配置
collection_name = 'food_ingredients'
dim = 4096  # 假设 Embedding 模型输出维度为 4096

# 连接 MySQL
mysql_conn = mysql.connector.connect(**mysql_config)
mysql_cursor = mysql_conn.cursor(dictionary=True)

# 连接 Milvus
connections.connect(host=milvus_config['host'], port=milvus_config['port'])

# 创建映射表
def create_mapping_table():
    try:
        mysql_cursor.execute('''
            CREATE TABLE IF NOT EXISTS milvus_mapping (
                milvus_id BIGINT PRIMARY KEY,
                original_data TEXT NOT NULL
            )
        ''')
        mysql_conn.commit()
        logging.info("成功创建映射表")
    except Exception as e:
        logging.error(f"创建映射表时出错: {e}")

def get_vector(text):
    """调用本地 Embedding 模型获取向量"""
    try:
        headers = {'Content-Type': 'application/json', 'Authorization': f"Bearer {embedding_config['apikey']}"}
        payload = {
            "model": embedding_config['model'],
            "input": text
        }
        response = requests.post(embedding_config['url'], json=payload, headers=headers)
        response.raise_for_status()
        return response.json()['data'][0]['embedding']
    except Exception as e:
        logging.error(f"获取向量时出错: {e}")
        return None

def create_milvus_collection():
    """创建 Milvus 集合"""
    try:
        # 检查集合是否存在
        existing_collections = list_collections()
        if collection_name in existing_collections:
            # 删除已存在的集合
            collection = Collection(name=collection_name)
            collection.drop()
            logging.info(f"删除已存在的集合 {collection_name}")
        # 定义字段
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=dim)
        ]
        schema = CollectionSchema(fields, "Food ingredients collection")
        # 创建集合
        collection = Collection(name=collection_name, schema=schema)
        logging.info(f"成功创建集合 {collection_name}")
        return collection
    except Exception as e:
        logging.error(f"创建集合时出错: {e}")
        return None

def insert_data_to_milvus(collection):
    """从 MySQL 导入数据到 Milvus"""
    try:
        table_fields = {
            "加工助剂数据库": "加工助剂名称",
            "可用于食品的菌种数据库": "可用于食品的菌种名称",
            "可用于婴幼儿食品的菌种数据库": "可用于婴幼儿食品的菌种名称",
            "三新食品原料数据库": "新食品原料名称",
            "添加剂数据库": "添加剂名称",
            "药食同源名单": "既是食品又是药品的物品名称",
            "营养强化剂数据库": "营养强化剂名称",
            "胶基数据库": "胶基配料",
            "酶制剂数据库": "酶制剂名称",
            "香精香料数据库": "香料中文名称",
            "中国食物成分表": "名称",
            "食品原料数据库": "食品原料名称"
        }
        for table, field in table_fields.items():
            # 确保查询的字段存在于表中
            query = f"SELECT {field} FROM {table}"
            mysql_cursor.execute(query)
            rows = mysql_cursor.fetchall()
            if not rows:
                logging.info(f"表 {table} 中没有数据")
                continue
            vectors = []
            original_data_list = []  # 存储原始数据
            for row in rows:
                value = row[field]  # 根据字段名称获取值
                vector = get_vector(value)
                if vector:
                    vectors.append(vector)
                    original_data_list.append(value)
                else:
                    logging.error(f"获取向量失败: {value}")
                # 每 1000 条数据插入一次，避免资源耗尽问题
                if len(vectors) == 1000:
                    try:
                        res = collection.insert([vectors])
                        # 将 Milvus ID 和原始数据插入映射表
                        insert_mapping(res.primary_keys, original_data_list)
                        logging.info(f"成功插入 1000 条数据到 Milvus 集合 {collection_name} (表: {table})")
                        vectors = []
                        original_data_list = []
                    except Exception as e:
                        logging.error(f"插入数据时出错 (表: {table}): {e}")
                        vectors = []
                        original_data_list = []
            if vectors:
                try:
                    res = collection.insert([vectors])
                    # 将 Milvus ID 和原始数据插入映射表
                    insert_mapping(res.primary_keys, original_data_list)
                    logging.info(f"成功插入 {len(vectors)} 条数据到 Milvus 集合 {collection_name} (表: {table})")
                except Exception as e:
                    logging.error(f"插入数据时出错 (表: {table}): {e}")
    except Exception as e:
        logging.error(f"插入数据时出错: {e}")

def insert_mapping(milvus_ids, original_data_list):
    """将 Milvus ID 和原始数据插入映射表"""
    try:
        query = "INSERT INTO milvus_mapping (milvus_id, original_data) VALUES (%s, %s)"
        values = [(milvus_id, original_data) for milvus_id, original_data in zip(milvus_ids, original_data_list)]
        mysql_cursor.executemany(query, values)
        mysql_conn.commit()
        logging.info(f"成功插入 {len(values)} 条映射记录")
    except Exception as e:
        logging.error(f"插入映射记录时出错: {e}")
        mysql_conn.rollback()

def create_index(collection):
    """为 Milvus 集合创建索引"""
    try:
        index_params = {
            'index_type': 'IVF_FLAT',
            'metric_type': 'L2',
            'params': {'nlist': 1024}
        }
        collection.create_index(field_name='vector', index_params=index_params)
        logging.info("成功创建索引")
    except Exception as e:
        logging.error(f"创建索引时出错: {e}")

def fuzzy_query_milvus(ingredient_name, top_k=3):
    """使用 Milvus 进行模糊查询"""
    try:
        vector = get_vector(ingredient_name)
        if not vector:
            return []
        collection = Collection(collection_name)
        # 加载集合
        collection.load()
        search_params = {
            "metric_type": "L2",
            "params": {"nprobe": 10}
        }
        results = collection.search([vector], anns_field='vector', param=search_params, limit=top_k)
        # 查询映射表获取原始数据
        milvus_ids = [result.id for result in results[0]]
        original_data = get_original_data(milvus_ids)
        return [{"name": original_data.get(milvus_id, f"未知 ID: {milvus_id}"), "distance": result.distance} for result, milvus_id in zip(results[0], milvus_ids)]
    except Exception as e:
        logging.error(f"查询时出错: {e}")
        return []

def get_original_data(milvus_ids):
    """从映射表中获取原始数据"""
    try:
        query = "SELECT milvus_id, original_data FROM milvus_mapping WHERE milvus_id IN ({})".format(
            ",".join(["%s"] * len(milvus_ids))
        )
        mysql_cursor.execute(query, milvus_ids)
        results = mysql_cursor.fetchall()
        return {row['milvus_id']: row['original_data'] for row in results}
    except Exception as e:
        logging.error(f"查询映射表时出错: {e}")
        return {}

def query_database(table, field, value):
    """查询 MySQL 数据库"""
    try:
        query = f"SELECT * FROM {table} WHERE {field} = %s"
        mysql_cursor.execute(query, (value,))
        return mysql_cursor.fetchall()
    except Exception as e:
        logging.error(f"查询数据库时出错: {e}")
        return []

def compliance_query_api(json_data):
    """合规查询 API"""
    try:
        data = json.loads(json_data)
        mapping_category_id = data.get('映射类别号')
        if mapping_category_id not in [2, 3, 5, 6, 7]:
            return {'食品基本信息': data}
        food_ingredients = data.get('食品配料', [])
        restricted_ingredients = []
        unrestricted_ingredients = []
        fuzzy_results = []
        unqueried_ingredients = []
        # 查询有限制要求的配料
        restricted_tables = [
            ("加工助剂数据库", "加工助剂名称"),
            ("可用于食品的菌种数据库", "可用于食品的菌种名称"),
            ("可用于婴幼儿食品的菌种数据库", "可用于婴幼儿食品的菌种名称"),
            ("三新食品原料数据库", "新食品原料名称"),
            ("添加剂数据库", "添加剂名称"),
            ("药食同源名单", "既是食品又是药品的物品名称"),
            ("营养强化剂数据库", "营养强化剂名称")
        ]
        for ingredient in food_ingredients:
            for table, field in restricted_tables:
                result = query_database(table, field, ingredient)
                if result:
                    restricted_ingredients.append({
                        '名称': ingredient,
                        '信息': result,
                        '类型': table
                    })
                    break
        # 查询无限制要求的配料
        unrestricted_tables = [
            ("胶基数据库", "胶基配料"),
            ("酶制剂数据库", "酶制剂名称"),
            ("香精香料数据库", "香料中文名称"),
            ("中国食物成分表", "名称"),
            ("食品原料数据库", "食品原料名称")
        ]
        for ingredient in food_ingredients:
            if any(ing['名称'] == ingredient for ing in restricted_ingredients):
                continue
            for table, field in unrestricted_tables:
                result = query_database(table, field, ingredient)
                if result:
                    unrestricted_ingredients.append({
                        '名称': ingredient,
                        '信息': result,
                        '类型': table
                    })
                    break
        # 模糊查询未查询到的配料
        for ingredient in food_ingredients:
            if not any(ing['名称'] == ingredient for ing in restricted_ingredients + unrestricted_ingredients):
                results = fuzzy_query_milvus(ingredient)
                if results:
                    fuzzy_results.extend([{
                        '名称': ingredient,
                        '信息': result,
                        '相似度': result['distance']
                    } for result in results])
                else:
                    unqueried_ingredients.append(ingredient)
        # 返回结果
        return {
            '食品基本信息': data,
            '有限制要求的配料及其信息': restricted_ingredients,
            '无限制要求的配料及其信息': unrestricted_ingredients,
            '模糊查询的配料及其信息': fuzzy_results,
            '未查询到的配料': unqueried_ingredients
        }
    except Exception as e:
        logging.error(f"API 调用时出错: {e}")
        return {'error': str(e)}

# 主函数
def main():
    # 创建映射表
    create_mapping_table()
    # 创建 Milvus 集合
    collection = create_milvus_collection()
    if not collection:
        return
    # 插入数据
    insert_data_to_milvus(collection)
    # 创建索引
    create_index(collection)
    # 加载集合
    try:
        collection.load()
        logging.info(f"成功加载集合 {collection_name}")
    except Exception as e:
        logging.error(f"加载集合时出错: {e}")

if __name__ == '__main__':
    main()
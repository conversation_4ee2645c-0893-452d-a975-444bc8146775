2025/08/07-15:29:15.174239 68d8 RocksDB version: 6.26.1
2025/08/07-15:29:15.174338 68d8 Git sha 0
2025/08/07-15:29:15.174350 68d8 Compile date 2021-12-13 18:23:52
2025/08/07-15:29:15.174360 68d8 DB SUMMARY
2025/08/07-15:29:15.174366 68d8 DB Session ID:  IXQ7AXG9LQ9PDKXUU6DG
2025/08/07-15:29:15.174495 68d8 SST files in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv dir, Total Num: 0, files: 
2025/08/07-15:29:15.174510 68d8 Write Ahead Log file in D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv: 
2025/08/07-15:29:15.174517 68d8                         Options.error_if_exists: 0
2025/08/07-15:29:15.174522 68d8                       Options.create_if_missing: 1
2025/08/07-15:29:15.174528 68d8                         Options.paranoid_checks: 1
2025/08/07-15:29:15.174617 68d8             Options.flush_verify_memtable_count: 1
2025/08/07-15:29:15.174621 68d8                               Options.track_and_verify_wals_in_manifest: 0
2025/08/07-15:29:15.174623 68d8                                     Options.env: 000002847530b880
2025/08/07-15:29:15.174625 68d8                                      Options.fs: WinFS
2025/08/07-15:29:15.174628 68d8                                Options.info_log: 0000028475396670
2025/08/07-15:29:15.174630 68d8                Options.max_file_opening_threads: 16
2025/08/07-15:29:15.174633 68d8                              Options.statistics: 0000000000000000
2025/08/07-15:29:15.174635 68d8                               Options.use_fsync: 0
2025/08/07-15:29:15.174637 68d8                       Options.max_log_file_size: 0
2025/08/07-15:29:15.174639 68d8                  Options.max_manifest_file_size: 1073741824
2025/08/07-15:29:15.174642 68d8                   Options.log_file_time_to_roll: 0
2025/08/07-15:29:15.174644 68d8                       Options.keep_log_file_num: 1000
2025/08/07-15:29:15.174646 68d8                    Options.recycle_log_file_num: 0
2025/08/07-15:29:15.174648 68d8                         Options.allow_fallocate: 1
2025/08/07-15:29:15.174650 68d8                        Options.allow_mmap_reads: 0
2025/08/07-15:29:15.174652 68d8                       Options.allow_mmap_writes: 0
2025/08/07-15:29:15.174655 68d8                        Options.use_direct_reads: 0
2025/08/07-15:29:15.174657 68d8                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/07-15:29:15.174659 68d8          Options.create_missing_column_families: 0
2025/08/07-15:29:15.174661 68d8                              Options.db_log_dir: 
2025/08/07-15:29:15.174663 68d8                                 Options.wal_dir: 
2025/08/07-15:29:15.174665 68d8                Options.table_cache_numshardbits: 6
2025/08/07-15:29:15.174668 68d8                         Options.WAL_ttl_seconds: 0
2025/08/07-15:29:15.174670 68d8                       Options.WAL_size_limit_MB: 0
2025/08/07-15:29:15.174672 68d8                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/07-15:29:15.174674 68d8             Options.manifest_preallocation_size: 4194304
2025/08/07-15:29:15.174676 68d8                     Options.is_fd_close_on_exec: 1
2025/08/07-15:29:15.174678 68d8                   Options.advise_random_on_open: 1
2025/08/07-15:29:15.174681 68d8                   Options.experimental_mempurge_threshold: 0.000000
2025/08/07-15:29:15.174726 68d8                    Options.db_write_buffer_size: 0
2025/08/07-15:29:15.174728 68d8                    Options.write_buffer_manager: 0000028475370610
2025/08/07-15:29:15.174730 68d8         Options.access_hint_on_compaction_start: 1
2025/08/07-15:29:15.174732 68d8  Options.new_table_reader_for_compaction_inputs: 0
2025/08/07-15:29:15.174735 68d8           Options.random_access_max_buffer_size: 1048576
2025/08/07-15:29:15.174737 68d8                      Options.use_adaptive_mutex: 0
2025/08/07-15:29:15.174739 68d8                            Options.rate_limiter: 0000000000000000
2025/08/07-15:29:15.174748 68d8     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/07-15:29:15.174751 68d8                       Options.wal_recovery_mode: 2
2025/08/07-15:29:15.174782 68d8                  Options.enable_thread_tracking: 0
2025/08/07-15:29:15.174786 68d8                  Options.enable_pipelined_write: 0
2025/08/07-15:29:15.174788 68d8                  Options.unordered_write: 0
2025/08/07-15:29:15.174790 68d8         Options.allow_concurrent_memtable_write: 1
2025/08/07-15:29:15.174793 68d8      Options.enable_write_thread_adaptive_yield: 1
2025/08/07-15:29:15.174795 68d8             Options.write_thread_max_yield_usec: 100
2025/08/07-15:29:15.174797 68d8            Options.write_thread_slow_yield_usec: 3
2025/08/07-15:29:15.174799 68d8                               Options.row_cache: None
2025/08/07-15:29:15.174801 68d8                              Options.wal_filter: None
2025/08/07-15:29:15.174803 68d8             Options.avoid_flush_during_recovery: 0
2025/08/07-15:29:15.174806 68d8             Options.allow_ingest_behind: 0
2025/08/07-15:29:15.174808 68d8             Options.preserve_deletes: 0
2025/08/07-15:29:15.174810 68d8             Options.two_write_queues: 0
2025/08/07-15:29:15.174812 68d8             Options.manual_wal_flush: 0
2025/08/07-15:29:15.174814 68d8             Options.atomic_flush: 0
2025/08/07-15:29:15.174816 68d8             Options.avoid_unnecessary_blocking_io: 0
2025/08/07-15:29:15.174818 68d8                 Options.persist_stats_to_disk: 0
2025/08/07-15:29:15.174820 68d8                 Options.write_dbid_to_manifest: 0
2025/08/07-15:29:15.174823 68d8                 Options.log_readahead_size: 0
2025/08/07-15:29:15.174825 68d8                 Options.file_checksum_gen_factory: Unknown
2025/08/07-15:29:15.174827 68d8                 Options.best_efforts_recovery: 0
2025/08/07-15:29:15.174829 68d8                Options.max_bgerror_resume_count: 2147483647
2025/08/07-15:29:15.174831 68d8            Options.bgerror_resume_retry_interval: 1000000
2025/08/07-15:29:15.174833 68d8             Options.allow_data_in_errors: 0
2025/08/07-15:29:15.174836 68d8             Options.db_host_id: __hostname__
2025/08/07-15:29:15.174838 68d8             Options.max_background_jobs: 2
2025/08/07-15:29:15.174840 68d8             Options.max_background_compactions: -1
2025/08/07-15:29:15.174842 68d8             Options.max_subcompactions: 1
2025/08/07-15:29:15.174844 68d8             Options.avoid_flush_during_shutdown: 0
2025/08/07-15:29:15.174846 68d8           Options.writable_file_max_buffer_size: 1048576
2025/08/07-15:29:15.174849 68d8             Options.delayed_write_rate : 16777216
2025/08/07-15:29:15.174851 68d8             Options.max_total_wal_size: 0
2025/08/07-15:29:15.174853 68d8             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/07-15:29:15.174855 68d8                   Options.stats_dump_period_sec: 600
2025/08/07-15:29:15.174857 68d8                 Options.stats_persist_period_sec: 600
2025/08/07-15:29:15.174859 68d8                 Options.stats_history_buffer_size: 1048576
2025/08/07-15:29:15.174862 68d8                          Options.max_open_files: -1
2025/08/07-15:29:15.174864 68d8                          Options.bytes_per_sync: 0
2025/08/07-15:29:15.174866 68d8                      Options.wal_bytes_per_sync: 0
2025/08/07-15:29:15.174868 68d8                   Options.strict_bytes_per_sync: 0
2025/08/07-15:29:15.174870 68d8       Options.compaction_readahead_size: 0
2025/08/07-15:29:15.174872 68d8                  Options.max_background_flushes: 1
2025/08/07-15:29:15.174874 68d8 Compression algorithms supported:
2025/08/07-15:29:15.174877 68d8 	kZSTD supported: 1
2025/08/07-15:29:15.174879 68d8 	kXpressCompression supported: 0
2025/08/07-15:29:15.174881 68d8 	kBZip2Compression supported: 1
2025/08/07-15:29:15.174884 68d8 	kZSTDNotFinalCompression supported: 1
2025/08/07-15:29:15.174886 68d8 	kLZ4Compression supported: 1
2025/08/07-15:29:15.174888 68d8 	kZlibCompression supported: 1
2025/08/07-15:29:15.174890 68d8 	kLZ4HCCompression supported: 1
2025/08/07-15:29:15.174892 68d8 	kSnappyCompression supported: 1
2025/08/07-15:29:15.174913 68d8 Fast CRC32 supported: Supported on x86
2025/08/07-15:29:15.176956 68d8 [db/db_impl/db_impl_open.cc:300] Creating manifest 1 
2025/08/07-15:29:15.179774 68d8 [db/version_set.cc:4847] Recovering from manifest file: D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv/MANIFEST-000001
2025/08/07-15:29:15.180358 68d8 [db/column_family.cc:609] --------------- Options for column family [default]:
2025/08/07-15:29:15.180365 68d8               Options.comparator: leveldb.BytewiseComparator
2025/08/07-15:29:15.180367 68d8           Options.merge_operator: None
2025/08/07-15:29:15.180369 68d8        Options.compaction_filter: None
2025/08/07-15:29:15.180371 68d8        Options.compaction_filter_factory: None
2025/08/07-15:29:15.180374 68d8  Options.sst_partitioner_factory: None
2025/08/07-15:29:15.180376 68d8         Options.memtable_factory: SkipListFactory
2025/08/07-15:29:15.180378 68d8            Options.table_factory: BlockBasedTable
2025/08/07-15:29:15.180454 68d8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000002847537e330)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0000028475322360
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 2566541721
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/07-15:29:15.180467 68d8        Options.write_buffer_size: 67108864
2025/08/07-15:29:15.180470 68d8  Options.max_write_buffer_number: 2
2025/08/07-15:29:15.180475 68d8        Options.compression[0]: NoCompression
2025/08/07-15:29:15.180478 68d8        Options.compression[1]: NoCompression
2025/08/07-15:29:15.180480 68d8        Options.compression[2]: ZSTD
2025/08/07-15:29:15.180482 68d8        Options.compression[3]: ZSTD
2025/08/07-15:29:15.180484 68d8        Options.compression[4]: ZSTD
2025/08/07-15:29:15.180487 68d8        Options.compression[5]: ZSTD
2025/08/07-15:29:15.180489 68d8        Options.compression[6]: ZSTD
2025/08/07-15:29:15.180491 68d8                  Options.bottommost_compression: Disabled
2025/08/07-15:29:15.180493 68d8       Options.prefix_extractor: nullptr
2025/08/07-15:29:15.180495 68d8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/07-15:29:15.180498 68d8             Options.num_levels: 7
2025/08/07-15:29:15.180500 68d8        Options.min_write_buffer_number_to_merge: 1
2025/08/07-15:29:15.180502 68d8     Options.max_write_buffer_number_to_maintain: 0
2025/08/07-15:29:15.180504 68d8     Options.max_write_buffer_size_to_maintain: 0
2025/08/07-15:29:15.180506 68d8            Options.bottommost_compression_opts.window_bits: -14
2025/08/07-15:29:15.180508 68d8                  Options.bottommost_compression_opts.level: 32767
2025/08/07-15:29:15.180511 68d8               Options.bottommost_compression_opts.strategy: 0
2025/08/07-15:29:15.180513 68d8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/07-15:29:15.180515 68d8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:29:15.180517 68d8         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/07-15:29:15.180520 68d8                  Options.bottommost_compression_opts.enabled: false
2025/08/07-15:29:15.180522 68d8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:29:15.180526 68d8            Options.compression_opts.window_bits: -14
2025/08/07-15:29:15.180529 68d8                  Options.compression_opts.level: 32767
2025/08/07-15:29:15.180531 68d8               Options.compression_opts.strategy: 0
2025/08/07-15:29:15.180533 68d8         Options.compression_opts.max_dict_bytes: 0
2025/08/07-15:29:15.180536 68d8         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/07-15:29:15.180538 68d8         Options.compression_opts.parallel_threads: 1
2025/08/07-15:29:15.180540 68d8                  Options.compression_opts.enabled: false
2025/08/07-15:29:15.180544 68d8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/07-15:29:15.180547 68d8      Options.level0_file_num_compaction_trigger: 4
2025/08/07-15:29:15.180550 68d8          Options.level0_slowdown_writes_trigger: 20
2025/08/07-15:29:15.180553 68d8              Options.level0_stop_writes_trigger: 36
2025/08/07-15:29:15.180556 68d8                   Options.target_file_size_base: 67108864
2025/08/07-15:29:15.180558 68d8             Options.target_file_size_multiplier: 1
2025/08/07-15:29:15.180560 68d8                Options.max_bytes_for_level_base: 268435456
2025/08/07-15:29:15.180562 68d8 Options.level_compaction_dynamic_level_bytes: 0
2025/08/07-15:29:15.180565 68d8          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/07-15:29:15.180567 68d8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/07-15:29:15.180570 68d8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/07-15:29:15.180572 68d8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/07-15:29:15.180574 68d8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/07-15:29:15.180576 68d8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/07-15:29:15.180578 68d8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/07-15:29:15.180580 68d8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/07-15:29:15.180583 68d8       Options.max_sequential_skip_in_iterations: 8
2025/08/07-15:29:15.180585 68d8                    Options.max_compaction_bytes: 1677721600
2025/08/07-15:29:15.180587 68d8                        Options.arena_block_size: 1048576
2025/08/07-15:29:15.180589 68d8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/07-15:29:15.180591 68d8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/07-15:29:15.180594 68d8       Options.rate_limit_delay_max_milliseconds: 100
2025/08/07-15:29:15.180596 68d8                Options.disable_auto_compactions: 0
2025/08/07-15:29:15.180599 68d8                        Options.compaction_style: kCompactionStyleLevel
2025/08/07-15:29:15.180601 68d8                          Options.compaction_pri: kMinOverlappingRatio
2025/08/07-15:29:15.180603 68d8 Options.compaction_options_universal.size_ratio: 1
2025/08/07-15:29:15.180605 68d8 Options.compaction_options_universal.min_merge_width: 2
2025/08/07-15:29:15.180608 68d8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/07-15:29:15.180610 68d8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/07-15:29:15.180612 68d8 Options.compaction_options_universal.compression_size_percent: -1
2025/08/07-15:29:15.180614 68d8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/07-15:29:15.180617 68d8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/07-15:29:15.180619 68d8 Options.compaction_options_fifo.allow_compaction: 0
2025/08/07-15:29:15.180625 68d8                   Options.table_properties_collectors: 
2025/08/07-15:29:15.180627 68d8                   Options.inplace_update_support: 0
2025/08/07-15:29:15.180630 68d8                 Options.inplace_update_num_locks: 10000
2025/08/07-15:29:15.180633 68d8               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/07-15:29:15.180636 68d8               Options.memtable_whole_key_filtering: 0
2025/08/07-15:29:15.180639 68d8   Options.memtable_huge_page_size: 0
2025/08/07-15:29:15.180642 68d8                           Options.bloom_locality: 0
2025/08/07-15:29:15.180669 68d8                    Options.max_successive_merges: 0
2025/08/07-15:29:15.180673 68d8                Options.optimize_filters_for_hits: 0
2025/08/07-15:29:15.180675 68d8                Options.paranoid_file_checks: 0
2025/08/07-15:29:15.180679 68d8                Options.force_consistency_checks: 1
2025/08/07-15:29:15.180682 68d8                Options.report_bg_io_stats: 0
2025/08/07-15:29:15.180684 68d8                               Options.ttl: 2592000
2025/08/07-15:29:15.180687 68d8          Options.periodic_compaction_seconds: 0
2025/08/07-15:29:15.180690 68d8                       Options.enable_blob_files: false
2025/08/07-15:29:15.180693 68d8                           Options.min_blob_size: 0
2025/08/07-15:29:15.180696 68d8                          Options.blob_file_size: 268435456
2025/08/07-15:29:15.180699 68d8                   Options.blob_compression_type: NoCompression
2025/08/07-15:29:15.180702 68d8          Options.enable_blob_garbage_collection: false
2025/08/07-15:29:15.180705 68d8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/07-15:29:15.180708 68d8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/07-15:29:15.182714 68d8 [db/version_set.cc:4887] Recovered from manifest file:D:\AAAAA-wq-work\access\milvus_data\data\rocketmq_meta_kv/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/07-15:29:15.182723 68d8 [db/version_set.cc:4902] Column family [default] (ID 0), log number is 0
2025/08/07-15:29:15.183019 68d8 [db/version_set.cc:4385] Creating manifest 4
2025/08/07-15:29:15.191130 68d8 [db/db_impl/db_impl_open.cc:1786] SstFileManager instance 00000284753a6450
2025/08/07-15:29:15.191194 68d8 DB pointer 00000284753b30a0
2025/08/07-15:29:15.191452 653c [db/db_impl/db_impl.cc:1004] ------- DUMPING STATS -------
2025/08/07-15:29:15.191464 653c [db/db_impl/db_impl.cc:1006] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x28475322360#14580 capacity: 2.39 GB collections: 1 last_copies: 0 last_secs: 3.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

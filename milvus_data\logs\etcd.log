{"level":"info","ts":"2025-08-07T15:29:15.204+0800","caller":"embed/etcd.go:124","msg":"configuring peer listeners","listen-peer-urls":["http://localhost:2380"]}
{"level":"info","ts":"2025-08-07T15:29:15.208+0800","caller":"embed/etcd.go:132","msg":"configuring client listeners","listen-client-urls":["http://localhost:2379"]}
{"level":"info","ts":"2025-08-07T15:29:15.209+0800","caller":"embed/etcd.go:306","msg":"starting an etcd server","etcd-version":"3.5.5","git-sha":"Not provided (use ./build instead of go build)","go-version":"go1.18","go-os":"windows","go-arch":"amd64","max-cpu-set":12,"max-cpu-available":12,"member-initialized":false,"name":"default","data-dir":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data","wal-dir":"","wal-dir-dedicated":"","member-dir":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data\\member","force-new-cluster":false,"heartbeat-interval":"100ms","election-timeout":"1s","initial-election-tick-advance":true,"snapshot-count":100000,"snapshot-catchup-entries":5000,"initial-advertise-peer-urls":["http://localhost:2380"],"listen-peer-urls":["http://localhost:2380"],"advertise-client-urls":["http://localhost:2379"],"listen-client-urls":["http://localhost:2379"],"listen-metrics-urls":[],"cors":["*"],"host-whitelist":["*"],"initial-cluster":"default=http://localhost:2380","initial-cluster-state":"new","initial-cluster-token":"etcd-cluster","quota-backend-bytes":2147483648,"max-request-bytes":1572864,"max-concurrent-streams":4294967295,"pre-vote":true,"initial-corrupt-check":false,"corrupt-check-time-interval":"0s","compact-check-time-enabled":false,"compact-check-time-interval":"1m0s","auto-compaction-mode":"","auto-compaction-retention":"0s","auto-compaction-interval":"0s","discovery-url":"","discovery-proxy":"","downgrade-check-interval":"5s"}
{"level":"info","ts":"2025-08-07T15:29:15.211+0800","caller":"etcdserver/backend.go:81","msg":"opened backend db","path":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data\\member\\snap\\db","took":"530.5µs"}
{"level":"info","ts":"2025-08-07T15:29:15.214+0800","caller":"wal/wal.go:281","msg":"closing WAL to release flock and retry directory renaming","from":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data\\member\\wal.tmp","to":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data\\member\\wal"}
{"level":"info","ts":"2025-08-07T15:29:15.218+0800","caller":"etcdserver/raft.go:494","msg":"starting local member","local-member-id":"8e9e05c52164694d","cluster-id":"cdf818194e3a8c32"}
{"level":"info","ts":"2025-08-07T15:29:15.218+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d switched to configuration voters=()"}
{"level":"info","ts":"2025-08-07T15:29:15.218+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became follower at term 0"}
{"level":"info","ts":"2025-08-07T15:29:15.218+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"newRaft 8e9e05c52164694d [peers: [], term: 0, commit: 0, applied: 0, lastindex: 0, lastterm: 0]"}
{"level":"info","ts":"2025-08-07T15:29:15.218+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became follower at term 1"}
{"level":"info","ts":"2025-08-07T15:29:15.218+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d switched to configuration voters=(10276657743932975437)"}
{"level":"warn","ts":"2025-08-07T15:29:15.220+0800","caller":"auth/store.go:1233","msg":"simple token is not cryptographically signed"}
{"level":"info","ts":"2025-08-07T15:29:15.222+0800","caller":"mvcc/kvstore.go:393","msg":"kvstore restored","current-rev":1}
{"level":"info","ts":"2025-08-07T15:29:15.223+0800","caller":"etcdserver/quota.go:94","msg":"enabled backend quota with default value","quota-name":"v3-applier","quota-size-bytes":2147483648,"quota-size":"2.1 GB"}
{"level":"info","ts":"2025-08-07T15:29:15.224+0800","caller":"etcdserver/server.go:854","msg":"starting etcd server","local-member-id":"8e9e05c52164694d","local-server-version":"3.5.5","cluster-version":"to_be_decided"}
{"level":"warn","ts":"2025-08-07T15:29:15.225+0800","caller":"etcdserver/metrics.go:224","msg":"failed to get file descriptor usage","error":"cannot get FDUsage on windows"}
{"level":"info","ts":"2025-08-07T15:29:15.225+0800","caller":"etcdserver/server.go:738","msg":"started as single-node; fast-forwarding election ticks","local-member-id":"8e9e05c52164694d","forward-ticks":9,"forward-duration":"900ms","election-ticks":10,"election-timeout":"1s"}
{"level":"info","ts":"2025-08-07T15:29:15.226+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d switched to configuration voters=(10276657743932975437)"}
{"level":"info","ts":"2025-08-07T15:29:15.226+0800","caller":"membership/cluster.go:421","msg":"added member","cluster-id":"cdf818194e3a8c32","local-member-id":"8e9e05c52164694d","added-peer-id":"8e9e05c52164694d","added-peer-peer-urls":["http://localhost:2380"]}
{"level":"info","ts":"2025-08-07T15:29:15.227+0800","caller":"embed/etcd.go:584","msg":"serving peer traffic","address":"127.0.0.1:2380"}
{"level":"info","ts":"2025-08-07T15:29:15.227+0800","caller":"embed/etcd.go:556","msg":"cmux::serve","address":"127.0.0.1:2380"}
{"level":"info","ts":"2025-08-07T15:29:15.227+0800","caller":"embed/etcd.go:275","msg":"now serving peer/client/metrics","local-member-id":"8e9e05c52164694d","initial-advertise-peer-urls":["http://localhost:2380"],"listen-peer-urls":["http://localhost:2380"],"advertise-client-urls":["http://localhost:2379"],"listen-client-urls":["http://localhost:2379"],"listen-metrics-urls":[]}
{"level":"info","ts":"2025-08-07T15:29:15.258+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d no leader at term 1; dropping index reading msg"}
{"level":"warn","ts":"2025-08-07T15:29:15.762+0800","caller":"etcdserver/v3_server.go:840","msg":"waiting for ReadIndex response took too long, retrying","sent-request-id":7587888637461779201,"retry-timeout":"500ms"}
{"level":"info","ts":"2025-08-07T15:29:15.762+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d no leader at term 1; dropping index reading msg"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d is starting a new election at term 1"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became pre-candidate at term 1"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d received MsgPreVoteResp from 8e9e05c52164694d at term 1"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became candidate at term 2"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d received MsgVoteResp from 8e9e05c52164694d at term 2"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became leader at term 2"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"raft.node: 8e9e05c52164694d elected leader 8e9e05c52164694d at term 2"}
{"level":"warn","ts":"2025-08-07T15:29:15.920+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"661.4713ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"","error":"etcdserver: leader changed"}
{"level":"info","ts":"2025-08-07T15:29:15.920+0800","caller":"traceutil/trace.go:171","msg":"trace[920232019] range","detail":"{range_begin:health; range_end:; }","duration":"661.4713ms","start":"2025-08-07T15:29:15.258+0800","end":"2025-08-07T15:29:15.920+0800","steps":["trace[920232019] 'agreement among raft nodes before linearized reading'  (duration: 661.4713ms)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:29:15.921+0800","caller":"etcdserver/server.go:2563","msg":"setting up initial cluster version using v2 API","cluster-version":"3.5"}
{"level":"warn","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"621.5278ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"warn","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"515.3171ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"warn","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"550.8678ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"warn","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"534.7175ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:1; }","duration":"515.3171ms","start":"2025-08-07T15:29:15.406+0800","end":"2025-08-07T15:29:15.922+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 515.3171ms)"],"step_count":1}
{"level":"warn","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"565.8935ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"traceutil/trace.go:171","msg":"trace[137588644] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:1; }","duration":"534.7175ms","start":"2025-08-07T15:29:15.387+0800","end":"2025-08-07T15:29:15.922+0800","steps":["trace[137588644] 'agreement among raft nodes before linearized reading'  (duration: 534.7175ms)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:1; }","duration":"565.8935ms","start":"2025-08-07T15:29:15.356+0800","end":"2025-08-07T15:29:15.922+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 565.8935ms)"],"step_count":1}
{"level":"warn","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"534.7175ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:1; }","duration":"534.7175ms","start":"2025-08-07T15:29:15.387+0800","end":"2025-08-07T15:29:15.922+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 534.7175ms)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/server.go:2054","msg":"published local member to cluster through raft","local-member-id":"8e9e05c52164694d","local-member-attributes":"{Name:default ClientURLs:[http://localhost:2379]}","request-path":"/0/members/8e9e05c52164694d/attributes","cluster-id":"cdf818194e3a8c32","publish-timeout":"7s"}
{"level":"warn","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"534.7175ms","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:1; }","duration":"534.7175ms","start":"2025-08-07T15:29:15.387+0800","end":"2025-08-07T15:29:15.922+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 534.7175ms)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:1; }","duration":"550.8678ms","start":"2025-08-07T15:29:15.371+0800","end":"2025-08-07T15:29:15.922+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 550.8678ms)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"traceutil/trace.go:171","msg":"trace[36037750] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:1; }","duration":"621.5278ms","start":"2025-08-07T15:29:15.300+0800","end":"2025-08-07T15:29:15.922+0800","steps":["trace[36037750] 'agreement among raft nodes before linearized reading'  (duration: 621.5278ms)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"embed/serve.go:100","msg":"ready to serve client requests"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"membership/cluster.go:584","msg":"set initial cluster version","cluster-id":"cdf818194e3a8c32","local-member-id":"8e9e05c52164694d","cluster-version":"3.5"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"api/capability.go:75","msg":"enabled capabilities for version","cluster-version":"3.5"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"etcdserver/server.go:2587","msg":"cluster version is updated","cluster-version":"3.5"}
{"level":"info","ts":"2025-08-07T15:29:15.922+0800","caller":"embed/serve.go:146","msg":"serving client traffic insecurely; this is strongly discouraged!","address":"127.0.0.1:2379"}
{"level":"info","ts":"2025-08-07T15:31:41.228+0800","caller":"embed/etcd.go:124","msg":"configuring peer listeners","listen-peer-urls":["http://localhost:2380"]}
{"level":"info","ts":"2025-08-07T15:31:41.231+0800","caller":"embed/etcd.go:132","msg":"configuring client listeners","listen-client-urls":["http://localhost:2379"]}
{"level":"info","ts":"2025-08-07T15:31:41.232+0800","caller":"embed/etcd.go:306","msg":"starting an etcd server","etcd-version":"3.5.5","git-sha":"Not provided (use ./build instead of go build)","go-version":"go1.18","go-os":"windows","go-arch":"amd64","max-cpu-set":12,"max-cpu-available":12,"member-initialized":true,"name":"default","data-dir":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data","wal-dir":"","wal-dir-dedicated":"","member-dir":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data\\member","force-new-cluster":false,"heartbeat-interval":"100ms","election-timeout":"1s","initial-election-tick-advance":true,"snapshot-count":100000,"snapshot-catchup-entries":5000,"initial-advertise-peer-urls":["http://localhost:2380"],"listen-peer-urls":["http://localhost:2380"],"advertise-client-urls":["http://localhost:2379"],"listen-client-urls":["http://localhost:2379"],"listen-metrics-urls":[],"cors":["*"],"host-whitelist":["*"],"initial-cluster":"","initial-cluster-state":"new","initial-cluster-token":"","quota-backend-bytes":2147483648,"max-request-bytes":1572864,"max-concurrent-streams":4294967295,"pre-vote":true,"initial-corrupt-check":false,"corrupt-check-time-interval":"0s","compact-check-time-enabled":false,"compact-check-time-interval":"1m0s","auto-compaction-mode":"","auto-compaction-retention":"0s","auto-compaction-interval":"0s","discovery-url":"","discovery-proxy":"","downgrade-check-interval":"5s"}
{"level":"info","ts":"2025-08-07T15:31:41.233+0800","caller":"etcdserver/backend.go:81","msg":"opened backend db","path":"D:\\AAAAA-wq-work\\access\\milvus_data\\data\\etcd.data\\member\\snap\\db","took":"0s"}
{"level":"info","ts":"2025-08-07T15:31:41.234+0800","caller":"etcdserver/server.go:530","msg":"No snapshot found. Recovering WAL from scratch!"}
{"level":"info","ts":"2025-08-07T15:31:41.234+0800","caller":"etcdserver/raft.go:529","msg":"restarting local member","cluster-id":"cdf818194e3a8c32","local-member-id":"8e9e05c52164694d","commit-index":53}
{"level":"info","ts":"2025-08-07T15:31:41.234+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d switched to configuration voters=()"}
{"level":"info","ts":"2025-08-07T15:31:41.234+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became follower at term 2"}
{"level":"info","ts":"2025-08-07T15:31:41.234+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"newRaft 8e9e05c52164694d [peers: [], term: 2, commit: 53, applied: 0, lastindex: 53, lastterm: 2]"}
{"level":"warn","ts":"2025-08-07T15:31:41.236+0800","caller":"auth/store.go:1233","msg":"simple token is not cryptographically signed"}
{"level":"info","ts":"2025-08-07T15:31:41.238+0800","caller":"mvcc/kvstore.go:393","msg":"kvstore restored","current-rev":32}
{"level":"info","ts":"2025-08-07T15:31:41.239+0800","caller":"etcdserver/quota.go:94","msg":"enabled backend quota with default value","quota-name":"v3-applier","quota-size-bytes":2147483648,"quota-size":"2.1 GB"}
{"level":"info","ts":"2025-08-07T15:31:41.240+0800","caller":"etcdserver/server.go:854","msg":"starting etcd server","local-member-id":"8e9e05c52164694d","local-server-version":"3.5.5","cluster-version":"to_be_decided"}
{"level":"warn","ts":"2025-08-07T15:31:41.240+0800","caller":"etcdserver/metrics.go:224","msg":"failed to get file descriptor usage","error":"cannot get FDUsage on windows"}
{"level":"info","ts":"2025-08-07T15:31:41.240+0800","caller":"etcdserver/server.go:754","msg":"starting initial election tick advance","election-ticks":10}
{"level":"info","ts":"2025-08-07T15:31:41.241+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d switched to configuration voters=(10276657743932975437)"}
{"level":"info","ts":"2025-08-07T15:31:41.241+0800","caller":"membership/cluster.go:421","msg":"added member","cluster-id":"cdf818194e3a8c32","local-member-id":"8e9e05c52164694d","added-peer-id":"8e9e05c52164694d","added-peer-peer-urls":["http://localhost:2380"]}
{"level":"info","ts":"2025-08-07T15:31:41.241+0800","caller":"membership/cluster.go:584","msg":"set initial cluster version","cluster-id":"cdf818194e3a8c32","local-member-id":"8e9e05c52164694d","cluster-version":"3.5"}
{"level":"info","ts":"2025-08-07T15:31:41.241+0800","caller":"api/capability.go:75","msg":"enabled capabilities for version","cluster-version":"3.5"}
{"level":"info","ts":"2025-08-07T15:31:41.242+0800","caller":"embed/etcd.go:275","msg":"now serving peer/client/metrics","local-member-id":"8e9e05c52164694d","initial-advertise-peer-urls":["http://localhost:2380"],"listen-peer-urls":["http://localhost:2380"],"advertise-client-urls":["http://localhost:2379"],"listen-client-urls":["http://localhost:2379"],"listen-metrics-urls":[]}
{"level":"info","ts":"2025-08-07T15:31:41.242+0800","caller":"embed/etcd.go:584","msg":"serving peer traffic","address":"127.0.0.1:2380"}
{"level":"info","ts":"2025-08-07T15:31:41.242+0800","caller":"embed/etcd.go:556","msg":"cmux::serve","address":"127.0.0.1:2380"}
{"level":"info","ts":"2025-08-07T15:31:41.265+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d no leader at term 2; dropping index reading msg"}
{"level":"warn","ts":"2025-08-07T15:31:41.769+0800","caller":"etcdserver/v3_server.go:840","msg":"waiting for ReadIndex response took too long, retrying","sent-request-id":7587888637499159297,"retry-timeout":"500ms"}
{"level":"info","ts":"2025-08-07T15:31:41.769+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d no leader at term 2; dropping index reading msg"}
{"level":"warn","ts":"2025-08-07T15:31:42.274+0800","caller":"etcdserver/v3_server.go:840","msg":"waiting for ReadIndex response took too long, retrying","sent-request-id":7587888637499159297,"retry-timeout":"500ms"}
{"level":"info","ts":"2025-08-07T15:31:42.274+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d no leader at term 2; dropping index reading msg"}
{"level":"info","ts":"2025-08-07T15:31:42.748+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d is starting a new election at term 2"}
{"level":"info","ts":"2025-08-07T15:31:42.748+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became pre-candidate at term 2"}
{"level":"info","ts":"2025-08-07T15:31:42.748+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d received MsgPreVoteResp from 8e9e05c52164694d at term 2"}
{"level":"info","ts":"2025-08-07T15:31:42.748+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became candidate at term 3"}
{"level":"info","ts":"2025-08-07T15:31:42.748+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d received MsgVoteResp from 8e9e05c52164694d at term 3"}
{"level":"info","ts":"2025-08-07T15:31:42.748+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"8e9e05c52164694d became leader at term 3"}
{"level":"info","ts":"2025-08-07T15:31:42.748+0800","logger":"raft","caller":"etcdserver/zap_raft.go:77","msg":"raft.node: 8e9e05c52164694d elected leader 8e9e05c52164694d at term 3"}
{"level":"warn","ts":"2025-08-07T15:31:42.749+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.4829911s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"","error":"etcdserver: leader changed"}
{"level":"info","ts":"2025-08-07T15:31:42.749+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; }","duration":"1.4834985s","start":"2025-08-07T15:31:41.265+0800","end":"2025-08-07T15:31:42.749+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 1.4829911s)"],"step_count":1}
{"level":"warn","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.4479656s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"warn","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.3571316s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"warn","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.3728801s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"traceutil/trace.go:171","msg":"trace[766123241] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:32; }","duration":"1.4479656s","start":"2025-08-07T15:31:41.302+0800","end":"2025-08-07T15:31:42.750+0800","steps":["trace[766123241] 'agreement among raft nodes before linearized reading'  (duration: 1.4479656s)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:32; }","duration":"1.3571316s","start":"2025-08-07T15:31:41.393+0800","end":"2025-08-07T15:31:42.750+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 1.3571316s)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"embed/serve.go:100","msg":"ready to serve client requests"}
{"level":"warn","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.3728801s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"traceutil/trace.go:171","msg":"trace[632565670] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:32; }","duration":"1.3728801s","start":"2025-08-07T15:31:41.377+0800","end":"2025-08-07T15:31:42.750+0800","steps":["trace[632565670] 'agreement among raft nodes before linearized reading'  (duration: 1.3728801s)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/server.go:2054","msg":"published local member to cluster through raft","local-member-id":"8e9e05c52164694d","local-member-attributes":"{Name:default ClientURLs:[http://localhost:2379]}","request-path":"/0/members/8e9e05c52164694d/attributes","cluster-id":"cdf818194e3a8c32","publish-timeout":"7s"}
{"level":"warn","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.3571316s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"traceutil/trace.go:171","msg":"trace[467839988] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:32; }","duration":"1.3571316s","start":"2025-08-07T15:31:41.393+0800","end":"2025-08-07T15:31:42.750+0800","steps":["trace[467839988] 'agreement among raft nodes before linearized reading'  (duration: 1.3571316s)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"traceutil/trace.go:171","msg":"trace[860249848] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:32; }","duration":"1.3728801s","start":"2025-08-07T15:31:41.377+0800","end":"2025-08-07T15:31:42.750+0800","steps":["trace[860249848] 'agreement among raft nodes before linearized reading'  (duration: 1.3728801s)"],"step_count":1}
{"level":"warn","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.3513639s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"traceutil/trace.go:171","msg":"trace[**********] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:32; }","duration":"1.3513639s","start":"2025-08-07T15:31:41.398+0800","end":"2025-08-07T15:31:42.750+0800","steps":["trace[**********] 'agreement among raft nodes before linearized reading'  (duration: 1.3513639s)"],"step_count":1}
{"level":"warn","ts":"2025-08-07T15:31:42.750+0800","caller":"etcdserver/util.go:166","msg":"apply request took too long","took":"1.3728801s","expected-duration":"100ms","prefix":"read-only range ","request":"key:\"health\" ","response":"range_response_count:0 size:4"}
{"level":"info","ts":"2025-08-07T15:31:42.750+0800","caller":"traceutil/trace.go:171","msg":"trace[798735580] range","detail":"{range_begin:health; range_end:; response_count:0; response_revision:32; }","duration":"1.3728801s","start":"2025-08-07T15:31:41.377+0800","end":"2025-08-07T15:31:42.750+0800","steps":["trace[798735580] 'agreement among raft nodes before linearized reading'  (duration: 1.3728801s)"],"step_count":1}
{"level":"info","ts":"2025-08-07T15:31:42.751+0800","caller":"embed/serve.go:146","msg":"serving client traffic insecurely; this is strongly discouraged!","address":"127.0.0.1:2379"}

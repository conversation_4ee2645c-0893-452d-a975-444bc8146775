from sqlalchemy import Column, Integer, String, Text, JSON
from .database import Base

class MappingTable(Base):
    __tablename__ = "2760映射表"
    
    id = Column(Integer, primary_key=True, index=True)
    分类号 = Column(String(255))
    食品类别 = Column(String(255))
    说明 = Column(Text)
    主要特征 = Column(Text)
    映射类别号 = Column(Integer)
    映射类别名称 = Column(String(255))

class NewFoodMaterial(Base):
    __tablename__ = "三新食品原料数据库"
    
    id = Column(Integer, primary_key=True, index=True)
    新食品原料名称 = Column(String(255))
    适宜人群 = Column(String(255))
    推荐食用量 = Column(Text)
    使用范围和最大使用量 = Column(Text)
    来源公告 = Column(String(255))
    质量要求 = Column(Text)
    食品安全指标 = Column(Text)

class FoodComposition(Base):
    __tablename__ = "中国食物成分表"
    
    id = Column(Integer, primary_key=True, index=True)
    名称 = Column(String(255))

class DairyAccessList(Base):
    __tablename__ = "乳制品准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    国家和地区 = Column(String(255))
    产品名称 = Column(String(255))
    准入状态 = Column(String(255))
    备注 = Column(Text)

class ProcessingAid(Base):
    __tablename__ = "加工助剂数据库"
    
    id = Column(Integer, primary_key=True, index=True)
    加工助剂名称 = Column(String(255))
    英文名称 = Column(String(255))
    功能 = Column(String(255))
    使用范围 = Column(Text)

class MedicinalMaterialAccess(Base):
    __tablename__ = "动植物源性药材准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    国家或地区 = Column(String(255))
    产品名称 = Column(String(255))
    产品描述 = Column(String(255))
    备注 = Column(String(255))

class AnimalEpidemic(Base):
    __tablename__ = "动物疫情名单"
    
    id = Column(Integer, primary_key=True, index=True)
    洲别 = Column(String(255))
    国家或地区 = Column(String(255))
    疫病 = Column(String(255))
    禁止进境物名称 = Column(Text)

class InfantProbiotics(Base):
    __tablename__ = "可用于婴幼儿食品的菌种数据库"
    
    id = Column(Integer, primary_key=True, index=True)
    可用于婴幼儿食品的菌种名称 = Column(String(255))
    拉丁名称 = Column(String(255))
    公告链接 = Column(String(255))
    适用食品类别 = Column(String(255))
    对标签的标注要求 = Column(Text)
    质量要求 = Column(Text)

class FoodProbiotics(Base):
    __tablename__ = "可用于食品的菌种数据库"
    
    id = Column(Integer, primary_key=True, index=True)
    可用于食品的菌种名称 = Column(String(255))
    拉丁名称 = Column(String(255))
    不适宜人群 = Column(String(255))
    适用的食品类别 = Column(String(255))
    公告链接 = Column(String(255))
    标签要求 = Column(Text)
    质量要求 = Column(Text)

class CoffeeCocoaAccess(Base):
    __tablename__ = "咖啡豆和可可豆的允许准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    种类 = Column(String(255))
    国家和地区 = Column(Text)

class FreshFruitAccess(Base):
    __tablename__ = "新鲜水果准入名单"

    id = Column(Integer, primary_key=True, index=True)
    分布 = Column(String(255))
    输出国家_地区 = Column("输出国家/地区", String(255))  # 使用column_name映射
    水果种类 = Column(JSON)

class VegetableSpiceAccess(Base):
    __tablename__ = "新鲜蔬菜、香辛料等植物准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    产品名称 = Column(String(255))
    国家和地区 = Column(String(255))
    备注 = Column(Text)

class PlantOriginAccess(Base):
    __tablename__ = "植物源性准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    国家和地区 = Column(String(255))
    产品名称 = Column(String(255))
    准入状态 = Column(String(255))
    备注 = Column(String(255))

class AquaticProductAccess(Base):
    __tablename__ = "水产品准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    国家和地区 = Column(String(255))
    产品名称 = Column(String(255))
    准入状态 = Column(String(255))
    备注 = Column(String(255))

class FoodAdditive(Base):
    __tablename__ = "添加剂数据库"

    id = Column(Integer, primary_key=True, index=True)
    添加剂名称 = Column(String(255))
    添加剂英文名称 = Column(Text)
    CNS号 = Column(String(255))
    INS号 = Column(String(255))
    功能 = Column(String(255))
    食品分类号 = Column(String(255))
    食品名称 = Column(String(255))
    最大使用量g_kg = Column("最大使用量g/kg", Text)  # 使用column_name映射
    备注 = Column(Text)

class MeatProductAccess(Base):
    __tablename__ = "肉制品准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    地区 = Column(String(255))
    产品大类 = Column(String(255))
    品名 = Column(JSON)
    注册企业 = Column(JSON)
    状态 = Column(String(255))

class GumBase(Base):
    __tablename__ = "胶基数据库"
    
    id = Column(Integer, primary_key=True, index=True)
    胶基配料 = Column(String(255))
    胶基配料的英文名称 = Column(String(255))
    所属类别 = Column(String(255))
    所属类别的英文名称 = Column(String(255))

class FoodMedicineSameOrigin(Base):
    __tablename__ = "药食同源名单"
    
    id = Column(Integer, primary_key=True, index=True)
    既是食品又是药品的物品名称 = Column(String(255))
    植物名 = Column(String(255))
    适用食品的类别 = Column(String(255))
    拉丁名称 = Column(String(255))
    公告链接 = Column(String(255))
    质量要求 = Column(String(255))
    对标签的标注要求 = Column(Text)

class NutrientFortifier(Base):
    __tablename__ = "营养强化剂数据库"

    id = Column(Integer, primary_key=True, index=True)
    营养强化剂名称 = Column(String(255))
    化合物来源 = Column(Text)
    应用范围 = Column(String(255))
    备注 = Column(Text)
    要求类型 = Column(String(255))  # 新增字段
    适用食品 = Column(String(255))
    在适用食品中的要求 = Column(Text)

class GrainFeedAccess(Base):
    __tablename__ = "进口粮食、动物饲料准入名单"
    
    id = Column(Integer, primary_key=True, index=True)
    类型 = Column(String(255))
    种类 = Column(String(255))
    输出国家或地区 = Column(Text)

class EnzymePreparation(Base):
    __tablename__ = "酶制剂数据库"

    id = Column(Integer, primary_key=True, index=True)
    酶制剂名称 = Column(String(255))
    英文名称 = Column(String(255))
    来源 = Column(String(255))
    供体 = Column(String(255))

class CasingAccess(Base):
    __tablename__ = "肠衣准入名单"

    id = Column(Integer, primary_key=True, index=True)
    国家或地区 = Column(String(255))
    产品名称 = Column(String(255))
    准入状态 = Column(String(255))

class BeeProductAccess(Base):
    __tablename__ = "蜂产品准入名单"

    id = Column(Integer, primary_key=True, index=True)
    国家或地区 = Column(String(255))
    产品名称 = Column(String(255))
    状态 = Column(String(255))

class BirdNestAccess(Base):
    __tablename__ = "燕窝准入名单"

    id = Column(Integer, primary_key=True, index=True)
    国家或地区 = Column(String(255))
    产品名称 = Column(String(255))
    准入状态 = Column(String(255))

class FlavorSpice(Base):
    __tablename__ = "香精香料数据库"

    id = Column(Integer, primary_key=True, index=True)
    编码 = Column(String(255))
    香料中文名称 = Column(String(255))
    香料英文名称 = Column(String(255))
    FEMA编号 = Column(String(255))

class FoodIngredient(Base):
    __tablename__ = "食品原料数据库"

    id = Column(Integer, primary_key=True, index=True)
    食品原料名称 = Column(String(255))
    基本信息 = Column(Text)
    来源 = Column(String(255))
    实施日期 = Column(String(255))
    有效性 = Column(String(255))
    备注 = Column(Text)
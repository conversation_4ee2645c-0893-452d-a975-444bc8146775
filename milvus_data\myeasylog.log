2025-08-07 15:29:15,934 INFO [default] [KNOWHERE][SetBlasThreshold][] Set faiss::distance_compute_blas_threshold to 16384
2025-08-07 15:29:15,934 INFO [default] [KNOWHERE][SetEarlyStopThreshold][] Set faiss::early_stop_threshold to 0
2025-08-07 15:29:15,934 INFO [default] [KNOWHERE][SetStatisticsLevel][] Set knowhere::STATISTICS_LEVEL to 0
2025-08-07 15:29:15,934 INFO [default] [KNOWHERE][SetBlasThreshold][] Set faiss::distance_compute_blas_threshold to 16384
2025-08-07 15:29:15,935 DEBUG [default] [SERVER][operator()][] Config easylogging with yaml file: D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml
2025-08-07 15:29:15,935 INFO [default] [KNOWHERE][SetEarlyStopThreshold][] Set faiss::early_stop_threshold to 0
2025-08-07 15:29:15,935 INFO [default] [KNOWHERE][SetStatisticsLevel][] Set knowhere::STATISTICS_LEVEL to 0
2025-08-07 15:29:15,935 DEBUG [default] [SERVER][operator()][] Config easylogging with yaml file: D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml
2025-08-07 15:29:15,935 INFO [default] [KNOWHERE][SetSimdType][] FAISS expect simdType::AUTO
2025-08-07 15:29:15,935 INFO [default] [KNOWHERE][SetSimdType][] FAISS hook REF
2025-08-07 15:29:15,935 DEBUG [default] [SEGCORE][SetIndexSliceSize][] set config index slice size(byte): 16777216
2025-08-07 15:29:15,935 DEBUG [default] [SEGCORE][SetThreadCoreCoefficient][] set thread pool core coefficient: 10
2025-08-07 15:29:15,935 DEBUG [default] [SEGCORE][SegcoreSetSimdType][] set config simd_type: auto
2025-08-07 15:29:15,935 INFO [default] [KNOWHERE][SetSimdType][] FAISS expect simdType::AUTO
2025-08-07 15:29:15,935 INFO [default] [KNOWHERE][SetSimdType][] FAISS hook REF
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetBlasThreshold][] Set faiss::distance_compute_blas_threshold to 16384
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetEarlyStopThreshold][] Set faiss::early_stop_threshold to 0
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetStatisticsLevel][] Set knowhere::STATISTICS_LEVEL to 0
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetBlasThreshold][] Set faiss::distance_compute_blas_threshold to 16384
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetEarlyStopThreshold][] Set faiss::early_stop_threshold to 0
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetStatisticsLevel][] Set knowhere::STATISTICS_LEVEL to 0
2025-08-07 15:31:42,764 DEBUG [default] [SERVER][operator()][] Config easylogging with yaml file: D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS expect simdType::AUTO
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS hook REF
2025-08-07 15:31:42,764 DEBUG [default] [SERVER][operator()][] Config easylogging with yaml file: D:\AAAAA-wq-work\access\milvus_data/configs/easylogging.yaml
2025-08-07 15:31:42,764 DEBUG [default] [SEGCORE][SegcoreSetSimdType][] set config simd_type: auto
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS expect simdType::AUTO
2025-08-07 15:31:42,764 INFO [default] [KNOWHERE][SetSimdType][] FAISS hook REF
2025-08-07 15:31:42,764 DEBUG [default] [SEGCORE][SetIndexSliceSize][] set config index slice size(byte): 16777216
2025-08-07 15:31:42,764 DEBUG [default] [SEGCORE][SetThreadCoreCoefficient][] set thread pool core coefficient: 10
